# 3.0.4
* Kompatibilität bei der Migration hinzugefügt

# 3.0.3
* Feld-Conversion-Booster für Google Ads hinzugefügt

# 3.0.2
* Implementierte Funktionalität zur Ermöglichung des Exports von Bestellungen mit negativem Gesamtwert.

# 3.0.1
* Verbesserungen bei der Auftragssynchronisation im Headless-Modus implementiert mit Shopware 6.5

# 3.0.0
* Kompatibilität mit Shopware 6.5

# 2.1.21
* Problem behoben, bei dem 'gclid' bei Verwendung von Varnish nicht festgelegt wurde

# 2.1.20
* ga4_session hinzugefügt
* Problem behoben, bei dem 'gclid' bei SalesChannelContextTokenChangeEvent gelöscht wurde

# 2.1.19
* Ändere 'executeQuery' auf 'execute', um die Kompatibilität mit Shopware <= 6.4.17 zu ermöglichen

# 2.1.18
* Besucherdaten wurden von sitzungsbasiert zu kontexttokenbasiert geändert.

# 2.1.17
* Protokollkanalkonstante hinzugefügt, die versehentlich entfernt wurde

# 2.1.16
* Behobenes Problem, das Tracking-Fehler auf bestimmten Subdomains mit bestimmten Pfadkonfigurationen verursachte

# 2.1.15
* Problem behoben, bei dem Besucherdaten auf zwischengespeicherten Seiten nicht verfolgt wurden

# 2.1.14
* Unterstützung für Cookie-Consent-Manager hinzugefügt: Cookiebot & CookieInformation, die auf die Marketingeinwilligung hört

# 2.1.13
* Bestellungen mit einem Gesamtbetrag von 0 sind jetzt vom Bestellexport ausgeschlossen
* Entfernen Sie die Verwendung von @RouteScope, die veraltet ist

# 2.1.12
* Max. Versuche zum Bestellexport hinzugefügt

# 2.1.11
* Problem behoben, bei dem Bestellungen nicht exportiert wurden, wenn eine Bestellung die Daten nicht konvertieren konnte (beinhaltet auch Protokollierungsmöglichkeit)
* Problem behoben, bei dem der Bestellexport unterbrochen wurde, wenn die Steuer fehlte

# 2.1.10
* Zusätzliche Validierung der Besucherdaten hinzugefügt

# 2.1.9
* Problem behoben, bei dem Cookies nicht gesetzt wurden, wenn alle Cookies akzeptiert wurden (über die Cookie-Leiste)
* Problem behoben, bei dem das Dekodierungscookie einfach ein neues Cookie erstellte, anstatt das alte Cookie wiederzuverwenden

# 2.1.8
* Ändern Sie die maximale Cua-Länge im Besucherdaten-Varchar-Feld von 255 auf 500 Zeichen

# 2.1.7
* OrderId zur Protokollierung hinzugefügt, wenn der Export der Bestellung fehlgeschlagen ist

# 2.1.6
* Problem behoben, bei dem gelöschte Produkte die geplante Aufgabe beendeten

# 2.1.5
* Problem behoben, bei dem das Exportdatum der Bestellung nicht aktualisiert wurde

# 2.1.4
* Problem behoben, bei dem der API-Test keinen Fehler ausgab, wenn die öffentliche ID leer war
* Problem behoben, bei dem die Schaltfläche "Bestellungen senden" keine Fehlermeldung bei schlechten Anmeldeinformationen ausgab

# 2.1.3
* Problem behoben, bei dem das Cookie nicht dekodiert wurde und Werte aktualisiert wurden

# 2.1.2
* Die Übersetzung von Schaltflächen auf der Konfigurationsseite wurde korrigiert

# 2.1.1
* Code durch die Verwendung von typisierten Eigenschaften PHP 7.4-konformer gemacht

# 2.1.0
* Geplante Aufgabe und Befehl hinzugefügt, um Besucherdaten zu bereinigen, die älter als 90 Tage sind
* Übersetzungen zu Plugin-Konfigurationselementen hinzugefügt
* Das Speichern von Tracking-Daten auf Web-Crawlern wurde gestoppt

# 2.0.1
* Fehler behoben, bei dem ein fehlender Routenbereich die Site beschädigen konnte

# 2.0.0
* Export von veralteten benutzerdefinierten Produkten entfernt

# 1.2.0
* pmTPTrack-Cookie zum Cookie-Consent-Manager hinzugefügt
* pmTPTrack-Cookie wird jetzt nur gesetzt, wenn der Kunde seine Zustimmung gegeben hat
* API-Test-Schaltfläche hinzugefügt

# 1.1.6
* Kaufpreis muss immer netto sein
* Preis als Brutto festlegen

# 1.1.5
* Bestellkriterien aktualisiert, um die Auswahl von State Matchine(s) zu unterstützen

# 1.1.4
* Konfigurationsfehler im Administrationsskript behoben

# 1.1.3
* Schleife über Elemente, um die aktuelle Konfiguration zu erhalten

# 1.1.2
* Korrigiertes Gewicht im Auftragsexport

# 1.1.1
* Aktualisieren Sie die Produktvergleichsvorlage, um zu überprüfen, ob ein Titelbild ausgewählt ist

# 1.1.0
* Veralteter benutzerdefinierter Produktexport, bei dem jetzt der Produktvergleich (Vertriebskanal) verwendet wird
* Codebereinigung
* Lieferadresse, Postleitzahl, Land und Gewicht hinzugefügt
* Es wurde möglich, verkaufskanalspezifische Bestellungen per Befehl/Auftragsexport zu exportieren

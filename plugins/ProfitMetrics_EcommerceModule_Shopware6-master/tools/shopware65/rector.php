<?php

declare(strict_types=1);

use <PERSON><PERSON>\Rector\Set\ShopwareSetList;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\SetList;
use <PERSON>\Symfony\Set\SymfonySetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->sets([
        // General PHP 8.1 compatibility
        SetList::PHP_81,
        SymfonySetList::SYMFONY_60,
        ShopwareSetList::SHOPWARE_6_5_0,
    ]);

    // Define paths to process
    $rectorConfig->paths([
        __DIR__ . '/../../src',
    ]);

    // Parallel execution for better performance
    $rectorConfig->parallel();
};

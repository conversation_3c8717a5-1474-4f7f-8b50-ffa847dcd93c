import CookieStorageHelper from 'src/helper/storage/cookie-storage.helper';
import {COOKIE_CONFIGURATION_UPDATE} from 'src/plugin/cookie/cookie-configuration.plugin';

function _pm_geturlparm(parmname) {
    var regex = new RegExp("[\\?&]" + parmname + "=([^&#]*)");
    var result = regex.exec(location.search);
    return result === null ? "" : decodeURIComponent(result[1].replace(/\+/g, " "));
}

function _pm_getGclid() {
    let gclid = _pm_geturlparm("gclid");

    if (gclid !== '') {
        return gclid;
    }
    let gclidfromGclAw = _pm_getcookie('_gcl_aw');
    if (gclidfromGclAw != null) {
        let gclAwSplitAll = gclidfromGclAw.split('.');
        if (gclAwSplitAll.length >= 3) {
            return gclidfromGclAw.substring(gclAwSplitAll[0].length + gclAwSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
        }
    }
    let gclidfromFPGCLAW = _pm_getcookie('FPGCLAW');
    if (gclidfromFPGCLAW != null) {
        const fpgSplitAll = gclidfromFPGCLAW.split('.');
        if (fpgSplitAll.length >= 3) {
            return gclidfromFPGCLAW.substring(fpgSplitAll[0].length + fpgSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
        }
    }
}

function _pm_getcookie(cookiename) {
    cookiename += "=";
    if (document.cookie.indexOf(cookiename) !== -1) {
        var idxofSource = document.cookie.indexOf(cookiename) + cookiename.length;
        var idxofEnd = document.cookie.indexOf(";", idxofSource);
        var cookval = "";
        if (idxofEnd === -1) {
            cookval = document.cookie.substr(idxofSource);
        } else {
            cookval = document.cookie.substr(idxofSource, idxofEnd - idxofSource);
        }
        if (cookval.length !== 0) {
            return cookval;
        } else {
            return null;
        }
    }
}

function pmGetGa4SessionId() {
    // example: _ga_333DA3AABC=GS1.1.1695210760.1.1.1695210809.11.0.0
    // above example was modified as it was taken from a customer, but format remains intact
    const retImploded = document.cookie.split(';')
        .filter((c) => c.indexOf('_ga_') !== -1)
        .map((c) => c.trim().split('.'))
        .filter((c) => null != c && typeof c !== 'undefined' && typeof c.length === 'number' && c.length >= 4) // must have atleast enough for count
        .map((c) => c[0].substring(4, c[0].indexOf('=')) + ":" + c[2]) // index 2 for session id
        .join(',')
    ;

    return null != retImploded && retImploded.length > 0 ? retImploded : null;
}

function pmGetGa4SessionCount() {
    const retImploded = document.cookie.split(';')
        .filter((c) => c.indexOf('_ga_') !== -1)
        .map((c) => c.trim().split('.'))
        .filter((c) => null != c && typeof c !== 'undefined' && typeof c.length === 'number' && c.length >= 4) // must have atleast enough for count
        .map((c) => c[0].substring(4, c[0].indexOf('=')) + ":" + c[3]) // index 3 for session count
        .join(',')
    ;

    return null != retImploded && retImploded.length > 0 ? retImploded : null;
}

function _pm_getStoredTPTrack() {
    var ret = _pm_getcookie("pmTPTrack");
    if (null != ret && ret.length > 0) {
        ret = JSON.parse(decodeURIComponent(ret));
    } else {
        ret = {gclid: null, gacid: null, gacid_source: null, fbp: null, fbc: null, gbraid: null, wbraid: null, sccid: null, ttclid: null, msclkid: null, twclid: null, ga4SessionId: null, ga4SessionCount: null, timestamp: (((new Date) / 1E3 | 0) - 100)};
    }

    return ret;
}

function _pm_storeTPTrack(tptrack) {
    var _pm_old_tpTrackCookVal = _pm_getcookie("pmTPTrack");

    var _pm_tpTrackCookVal = encodeURIComponent(JSON.stringify(tptrack));
    document.cookie = "pmTPTrack=" + _pm_tpTrackCookVal + "; path=/";

}

function _pm_GetGacidFromTracker() {
    if (typeof ga == 'function') {
        try {
            ga(function (tracker) {
                var gacid = tracker.get('clientId');
                if (null != gacid) {

                    var _pm_curPMTPTrack = _pm_getStoredTPTrack();
                    if (_pm_curPMTPTrack.gacid !== gacid) {
                        _pm_curPMTPTrack.gacid = gacid;
                        _pm_curPMTPTrack.gacid_source = "gatracker";
                        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;

                        _pm_storeTPTrack(_pm_curPMTPTrack);
                    }
                }
            });
        } catch (eee) {
        }
    } else {
        setTimeout(_pm_GetGacidFromTracker, 100);
    }
}

function load_pmTPTrack() {
    var _pm_curPMTPTrack = _pm_getStoredTPTrack();

    var _pm_newFBC = _pm_getcookie("_fbc");
    if (null != _pm_newFBC && _pm_curPMTPTrack.fbc !== _pm_newFBC) {
        _pm_curPMTPTrack.fbc = _pm_newFBC;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_newFBP = _pm_getcookie("_fbp");
    if (null != _pm_newFBP && _pm_curPMTPTrack.fbp !== _pm_newFBP) {
        _pm_curPMTPTrack.fbp = _pm_newFBP;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_newGacid = _pm_getcookie("_ga");
    if (null != _pm_newGacid && _pm_curPMTPTrack.gacid_source !== "gatracker" && _pm_curPMTPTrack.gacid !== _pm_newGacid) {
        _pm_curPMTPTrack.gacid = _pm_newGacid;
        _pm_curPMTPTrack.gacid_source = "gacookie";
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_newGclid = _pm_getGclid();
    if (_pm_newGclid !== "") {
        _pm_curPMTPTrack.gclid = _pm_newGclid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_gbraid = _pm_geturlparm("gbraid");
    if (_pm_gbraid !== "") {
        _pm_curPMTPTrack.gbraid = _pm_gbraid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_wbraid = _pm_geturlparm("wbraid");
    if (_pm_wbraid !== "") {
        _pm_curPMTPTrack.wbraid = _pm_wbraid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_sccid = _pm_geturlparm("sccid");
    if (_pm_sccid !== "") {
        _pm_curPMTPTrack.sccid = _pm_sccid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_ttclid = _pm_geturlparm("ttclid");
    if (_pm_ttclid !== "") {
        _pm_curPMTPTrack.ttclid = _pm_ttclid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_msclkid = _pm_geturlparm("msclkid");
    if (_pm_msclkid !== "") {
        _pm_curPMTPTrack.msclkid = _pm_msclkid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_twclid = _pm_geturlparm("twclid");
    if (_pm_twclid !== "") {
        _pm_curPMTPTrack.twclid = _pm_twclid;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_ga4SessionId = pmGetGa4SessionId();
    if (_pm_ga4SessionId != null && _pm_ga4SessionId !== "") {
        _pm_curPMTPTrack.ga4SessionId = _pm_ga4SessionId;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    var _pm_ga4SessionCount = pmGetGa4SessionCount();
    if (_pm_ga4SessionCount != null && _pm_ga4SessionCount !== "") {
        _pm_curPMTPTrack.ga4SessionCount = _pm_ga4SessionCount;
        _pm_curPMTPTrack.timestamp = (new Date) / 1E3 | 0;
    }

    // Temp solution
    _pm_curPMTPTrack.cc_marketing = window.cc_marketing;
    _pm_curPMTPTrack.cc_statistics = window.cc_statistics;

    _pm_storeTPTrack(_pm_curPMTPTrack);
    _pm_GetGacidFromTracker();

    // Set previousDecision
    localStorage.setItem('pfm-consent-granted', true);
}

// Set default consent state
window.cc_statistics = false;
window.cc_marketing = false;
window.addEventListener("load", function (_event) {
    let previousDecision = localStorage.getItem('pfm-consent-granted');

    if (!window.blockScriptBeforeConsent || previousDecision) {
        // Set default consent to true
        window.cc_statistics = true;
        window.cc_marketing = true;
        load_pmTPTrack()
    }

    //Shopware
    document.addEventListener('CookieConfiguration_Update', (event) => {
        const { pmTPTrack } = event.detail;

        if (pmTPTrack === true) {
            window.cc_statistics = true;
            window.cc_marketing = true;
            load_pmTPTrack()
        }
    });

    // CookieBot
    // Get consent
    if (typeof Cookiebot !== 'undefined' && (Cookiebot?.consent?.statistics && Cookiebot?.consent?.marketing)) {
        cc_statistics = Cookiebot.consent.statistics;
        cc_marketing = Cookiebot.consent.marketing;
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener('CookiebotOnConsentReady', function() {
            if (Cookiebot?.consent?.statistics && Cookiebot?.consent?.marketing) {
                cc_statistics = Cookiebot.consent.statistics;
                cc_marketing = Cookiebot.consent.marketing;
                load_pmTPTrack();
            }
        });
    }

    // CookieInformation
    // Get consent
    if (typeof CookieInformation !== 'undefined' && (CookieInformation?.getConsentGivenFor('cookie_cat_statistic') && CookieInformation?.getConsentGivenFor('cookie_cat_marketing'))) {
        cc_statistics = CookieInformation.getConsentGivenFor('cookie_cat_statistic');
        cc_marketing = CookieInformation.getConsentGivenFor('cookie_cat_marketing');
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener('CookieInformationConsentGiven', function() {
            if (CookieInformation?.getConsentGivenFor('cookie_cat_statistic') && CookieInformation?.getConsentGivenFor('cookie_cat_marketing')) {
                cc_statistics = CookieInformation.getConsentGivenFor('cookie_cat_statistic');
                cc_marketing = CookieInformation.getConsentGivenFor('cookie_cat_marketing');
                load_pmTPTrack();
            }
        });
    }

    // OneTrust
    // Get consent
    if (typeof OneTrust !== 'undefined' && (OnetrustActiveGroups?.includes("2") && OnetrustActiveGroups?.includes("4"))) {
        cc_statistics = OnetrustActiveGroups.includes("2");
        cc_marketing = OnetrustActiveGroups.includes("4");
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("OneTrustGroupsUpdated", event => {
            if (event?.detail?.some(group => group.includes("4")) && event?.detail?.some(group => group.includes("2"))) {
                cc_statistics = event.detail.some(group => group.includes("4"));
                cc_marketing = event.detail.some(group => group.includes("2"));
                load_pmTPTrack();
            }
        });
    }

    // CookieYes
    // Get consent
    if (typeof getCkyConsent !== 'undefined' && (getCkyConsent()?.categories?.analytics && getCkyConsent()?.categories?.advertisement)) {
        cc_statistics = getCkyConsent().categories.analytics;
        cc_marketing = getCkyConsent().categories.advertisement;
        load_pmTPTrack();
    } else {
        // Add event listener
        document.addEventListener("cookieyes_consent_update", function(eventData) {
            if (eventData?.detail?.accepted?.includes("analytics") && eventData?.detail?.accepted?.includes("advertisement")) {
                cc_statistics = eventData.detail.accepted.includes("analytics");
                cc_marketing = eventData.detail.accepted.includes("advertisement");
                load_pmTPTrack();
            }
        });
    }

    // CookieFirst
    // Get consent
    if (typeof CookieFirst !== 'undefined' && (CookieFirst?.consent?.performance && CookieFirst?.consent?.advertising)) {
        cc_statistics = CookieFirst.consent.performance;
        cc_marketing = CookieFirst.consent.advertising;
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener('cf_consent', function(event) {
            if (event?.detail?.performance && event?.detail?.advertising) {
                cc_statistics = event.detail.performance;
                cc_marketing = event.detail.advertising;
                load_pmTPTrack();
            }
        });
        // Add event listener
        window.addEventListener('cf_consent_loaded', function(event) {
            if (event?.detail?.performance && event?.detail?.advertising) {
                cc_statistics = event.detail.performance;
                cc_marketing = event.detail.advertising;
                load_pmTPTrack();
            }
        });
    }

    // CookieScript
    // Get consent
    if (typeof CookieScript !== 'undefined' && (CookieScript?.instance?.currentState()?.categories?.includes("performance") && CookieScript?.instance?.currentState()?.categories?.includes("targeting"))) {
        cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
        cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener('CookieScriptCategory-strict', function() {
            if (CookieScript?.instance?.currentState()?.categories?.includes("performance") && CookieScript?.instance?.currentState()?.categories?.includes("targeting")) {
                cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
                cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
                load_pmTPTrack();
            }
        });
    }


    // Google Consent Mode
    // Get consent
    if (typeof window.google_tag_data !== 'undefined' && window.google_tag_data?.ics && (window.google_tag_data?.ics?.entries?.ad_storage?.update && window.google_tag_data?.ics?.entries?.analytics_storage?.update)) {
        cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
        cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
        load_pmTPTrack();
    } else {
        // Add event listener
        window.google_tag_data?.ics?.addListener(["ad_storage", "analytics_storage"], function(event) {
            if (window.google_tag_data?.ics?.entries?.ad_storage?.update && window.google_tag_data?.ics?.entries?.analytics_storage?.update) {
                cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
                cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
                load_pmTPTrack();
            }
        });
    }

});

// Necessary for the webpack hot module reloading server
if (module.hot) {
    module.hot.accept();
}

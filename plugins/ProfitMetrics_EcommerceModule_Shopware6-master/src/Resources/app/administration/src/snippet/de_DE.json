{"profitmetrics-api": {"order": {"title": "profitmetrics-api.order.title", "success": "profitmetrics-api.order.success", "error": "profitmetrics-api.order.error"}, "test": {"title": "Test API connection", "success": "Verbindung wurde erfolgreich getestet", "error": "Verbindung konnte nicht hergestellt werden. Bitte prüfe die Zugangsdaten"}}, "profitmetrics": {"send-orders": {"label": "Bestellungen senden"}, "api-test": {"label": "API-Verbindung testen"}, "customer-download-button": {"label": "<PERSON><PERSON> herunt<PERSON>n"}}, "sw-sales-channel": {"detail": {"productComparison": {"templates": {"template-label": {"profitMetrics": "ProfitMetrics"}}}}}}
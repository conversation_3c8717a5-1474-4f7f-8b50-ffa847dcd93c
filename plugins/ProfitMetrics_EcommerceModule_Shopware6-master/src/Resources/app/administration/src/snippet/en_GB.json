{"profitmetrics-api": {"order": {"title": "profitmetrics-api.order.title", "success": "profitmetrics-api.order.success", "error": "profitmetrics-api.order.error"}, "test": {"title": "Test API connection", "success": "Connection tested with success", "error": "Connection could not be established."}}, "profitmetrics": {"send-orders": {"label": "Send orders now"}, "api-test": {"label": "Test API connection"}, "customer-download-button": {"label": "Download customers"}}, "sw-sales-channel": {"detail": {"productComparison": {"templates": {"template-label": {"profitMetrics": "ProfitMetrics"}}}}}}
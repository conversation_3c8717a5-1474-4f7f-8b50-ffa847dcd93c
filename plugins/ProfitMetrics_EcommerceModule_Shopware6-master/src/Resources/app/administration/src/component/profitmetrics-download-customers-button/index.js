const { Component, Mixin } = Shopware;
import template from './profitmetrics-download-customers-button.html.twig';

Component.register('profitmetrics-download-customers-button', {
    template: template,

    inject: ['profitMetricsDownloadCustomersService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isSaveSuccessful: false,
        };
    },

    computed: {
        pluginConfig() {
            let config = null;
            let component = this;

            do {
                component = component.$parent;
                config = component.actualConfigData;
            } while (!config)

            const currentSalesChannelId = component.currentSalesChannelId;
            return config[currentSalesChannelId];
        }
    },

    methods: {
        saveFinish() {
            this.isSaveSuccessful = false;
        },

        downloadCustomers() {
            this.isLoading = true;
            this.profitMetricsDownloadCustomersService
                .downloadCustomers(this.pluginConfig)
                .then((res) => {
                    const blob = new Blob([res], { type: 'application/csv' });
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    const now = new Date();
                    link.download = 'pm_customers_'+ (now.toLocaleDateString('sv').replace(/-/g, '') + '_' + now.toTimeString().slice(0, 8).replace(/:/g, '')) +'.csv';
                    document.body.appendChild(link);
                    link.click();

                    document.body.removeChild(link);

                })
                .catch(() => {
                    this.createNotificationError({
                        title: this.$tc('profitmetrics-api.test.title'),
                        message: this.$tc('profitmetrics-api.test.error')
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
})

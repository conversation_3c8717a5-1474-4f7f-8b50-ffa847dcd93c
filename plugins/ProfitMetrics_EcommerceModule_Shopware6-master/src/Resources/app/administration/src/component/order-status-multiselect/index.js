const { Component } = Shopware;
const { Criteria , EntityCollection} = Shopware.Data;

Component.extend('order-status-multiselect', 'sw-entity-multi-id-select',  {
    inheritAttrs: false,
    inject: ['repositoryFactory'],
    props: {
        entity: {
            type: String,
            required: false,
            default: 'state_machine',
        },

        criteria: {
            type: Object,
            required: false,
            default() {
                const criteria = new Criteria();
                criteria.addAssociation('stateMachine')
                return criteria;
            },
        },
        repository: {
            type: Object,
            required: false,
            default() {
               return this.repositoryFactory.create('state_machine_state');
            },
        },
    },
});

Component.override('sw-entity-multi-select', {
    methods: {
        displayLabelProperty(item) {
            if (this.$parent.$attrs.componentName !== 'order-status-multiselect') {
                return this.$super('displayLabelProperty', item);
            }


            let state = this.getKey(item, 'name') || this.getKey(item, `translated.name`);
            let stateMachine = this.getKey(item.stateMachine, 'name') || this.getKey(item.stateMachine, `translated.name`);

            //fix order_delivery.state and order_state have same translation in shopware db
            if (item.stateMachine.technicalName === 'order_delivery.state' && item.stateMachine.name === 'Order state') {
                stateMachine = 'Order delivery state';
            }
            return stateMachine + ' - ' + state;
        },
    }
});

const { Component, Mixin } = Shopware;
import template from './profitmetrics-api-test-button.html.twig';

Component.register('profitmetrics-api-test-button', {
    template: template,

    inject: ['profitMetricsApiService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isSaveSuccessful: false,
        };
    },

    computed: {
        pluginConfig() {
            let config = null;
            let component = this;

            do {
                component = component.$parent;
                config = component.actualConfigData;
            } while (!config)

            const currentSalesChannelId = component.currentSalesChannelId;
            return config[currentSalesChannelId];
        }
    },

    methods: {
        saveFinish() {
            this.isSaveSuccessful = false;
        },

        testApi() {
            this.isLoading = true;
            this.profitMetricsApiService
                .testConfig(this.pluginConfig)
                .then((res) => {
                    if (res.isValid) {
                        this.isSaveSuccessful = true;
                        this.createNotificationSuccess({
                            title: this.$tc('profitmetrics-api.test.title'),
                            message: this.$tc('profitmetrics-api.test.success')
                        });
                    } else {
                        this.createNotificationError({
                            title: this.$tc('profitmetrics-api.test.title'),
                            message: this.$tc('profitmetrics-api.test.error')
                        });
                    }
                })
                .catch(() => {
                    this.createNotificationError({
                        title: this.$tc('profitmetrics-api.test.title'),
                        message: this.$tc('profitmetrics-api.test.error')
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
})

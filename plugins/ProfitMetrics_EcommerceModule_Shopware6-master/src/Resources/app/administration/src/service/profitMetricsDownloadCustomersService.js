const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class ProfitMetricsDownloadCustomersService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'profitmetrics-api') {
        super(httpClient, loginService, apiEndpoint);
    }

    downloadCustomers(data) {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .get(
                `_action/${this.getApiBasePath()}/customers-export`,
                data,
                headers
            ).then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

Application.addServiceProvider('profitMetricsDownloadCustomersService', (container) => {
    const initContainer = Application.getContainer('init');
    return new ProfitMetricsDownloadCustomersService(initContainer.httpClient, container.loginService);
});

const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class ProfitMetricsApiService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'profitmetrics-api') {
        super(httpClient, loginService, apiEndpoint);
    }

    testConfig(data) {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .post(
                `_action/${this.getApiBasePath()}/verify`,
                data,
                headers
            ).then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

Application.addServiceProvider('profitMetricsApiService', (container) => {
    const initContainer = Application.getContainer('init');
    return new ProfitMetricsApiService(initContainer.httpClient, container.loginService);
});

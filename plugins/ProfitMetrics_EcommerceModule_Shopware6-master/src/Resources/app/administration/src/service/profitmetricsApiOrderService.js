const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class OrderApiClient extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'profitmetrics-api') {
        super(httpClient, loginService, apiEndpoint);
    }

    exportOrders(values) {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .post(`_action/${this.getApiBasePath()}/order-export`, values,{
                headers
            })
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

Application.addServiceProvider('profitmetricsApiOrder', (container) => {
    const initContainer = Application.getContainer('init');
    return new OrderApiClient(initContainer.httpClient, container.loginService);
});

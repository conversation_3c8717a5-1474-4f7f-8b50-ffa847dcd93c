import header from './header.xml.twig';
import body from './body.xml.twig';
import footer from './footer.xml.twig';

Shopware.Service('exportTemplateService').registerProductExportTemplate({
    name: 'profitMetrics',
    translationKey: 'sw-sales-channel.detail.productComparison.templates.template-label.profitMetrics',
    headerTemplate: header.trim(),
    bodyTemplate: body,
    footerTemplate: footer.trim(),
    fileName: 'profitMetrics.xml',
    encoding: 'UTF-8',
    fileFormat: 'xml',
    includeVariants: true,
    generateByCronjob: false,
    interval: 86400,
});

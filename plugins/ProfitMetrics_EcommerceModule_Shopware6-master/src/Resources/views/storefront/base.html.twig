{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_body_script %}
    {{ parent() }}

    {% block profitmatrix_block_script_before_variable_init %}
        <script>
            blockScriptBeforeConsent = {% if config('Profitmetrics.config.blockBeforeConsent') %} true {% else %} false {% endif %};
            headlessMode = {% if config('Profitmetrics.config.headlessMode') %} true {% else %} false {% endif %};
        </script>
    {% endblock %}

    {% block profitmatrix_all_page_js_wrapper %}
        {% if config('Profitmetrics.config.allPagesJavascript') %}
            {% block profitmatrix_all_page_js_content %}
                {{ config('Profitmetrics.config.allPagesJavascript')|raw }}
            {% endblock %}
        {% endif %}
    {% endblock %}

    {% if page.order.customFields.google_ads_script %}
        <script type="text/javascript">
            {{ page.order.customFields.google_ads_script|raw }}
        </script>
    {% endif %}

    {% if config('Profitmetrics.config.googleConversionId') %}
        {% set googleConversionId =  config('Profitmetrics.config.googleConversionId') %}
        <!-- ProfitMetrics Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ googleConversionId }}"></script>
        <script>
            function formatAWID(awID) {
                // Ensure the AW ID is in the correct format
                if (!awID.startsWith('AW-')) {
                    awID = 'AW-' + awID.replace(/^AW/, '');
                }
                return awID;
            }

            const inputAWID = '{{ googleConversionId }}';
            const awID = formatAWID(inputAWID);

            function _pm_executeAWConfig() {
                const scriptExists = document.querySelector('script[src^="https://www.googletagmanager.com/gtag/js?id="]');
                let foundJsDate = false;
                if (typeof dataLayer !== 'undefined') {
                    for (var i = 0; i <= dataLayer.length; i++) {
                        if (typeof dataLayer[i] === 'object' && dataLayer[i].length && dataLayer[i].length > 2 && dataLayer[i][0] === 'js') {
                            foundJsDate = true;
                            break;
                        }
                    }
                }
                if (scriptExists && foundJsDate) {
                    window.dataLayer = window.dataLayer || [];
                    function gtag() {
                        dataLayer.push(arguments);
                    }
                    gtag('config', awID);
                } else {
                    const script = document.createElement('script');
                    script.async = true;
                    script.id = 'profitmetrics-gawid-script-' + awID;
                    script.src = 'https://www.googletagmanager.com/gtag/js?id=' + awID;
                    document.head.appendChild(script);
                    script.addEventListener('load', function() {
                        window.dataLayer = window.dataLayer || [];
                        function gtag() {
                            dataLayer.push(arguments);
                        }
                        gtag('js', new Date());
                        gtag('config', awID);
                    });
                }
            }
            _pm_executeAWConfig();
        </script>
    {% endif %}

    <!--- INSERT CONVERSION BOOSTER --->
{% endblock %}

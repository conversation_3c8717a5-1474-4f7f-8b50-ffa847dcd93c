!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p=(window.__sw__.assetPath + '/bundles/profitmetrics/'),r(r.s="AYp/")}({"//J3":function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(o=i.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===r(c)?c:String(c)),i)}var o,c}function o(e,t){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,p=Shopware.Application,l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(p,e);var t,r,s,a=c(p);function p(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,p),a.call(this,e,t,r)}return t=p,(r=[{key:"testConfig",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.post("_action/".concat(this.getApiBasePath(),"/verify"),e,t).then((function(e){return u.handleResponse(e)}))}}])&&i(t.prototype,r),s&&i(t,s),Object.defineProperty(t,"prototype",{writable:!1}),p}(u);p.addServiceProvider("profitMetricsApiService",(function(e){var t=p.getContainer("init");return new l(t.httpClient,e.loginService)}))},"AYp/":function(e,t,r){"use strict";r.r(t);r("Id+x"),r("//J3"),r("q+va");var n=Shopware,i=n.Component,o=n.Mixin;i.register("profitmatrix-api-send-orders",{template:'<div>\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        variant="primary"\n        @process-finish="isSaveSuccessful = false"\n        @click="exportOrders"\n    >{{ $tc(\'profitmetrics.send-orders.label\') }}</sw-button-process>\n</div>\n',inject:["profitmetricsApiOrder"],mixins:[o.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){for(var e=this;e.$parent&&!e.actualConfigData;)e=e.$parent;return e.actualConfigData?e.actualConfigData.null:null}},methods:{exportOrders:function(){var e=this;this.isLoading=!0,this.profitmetricsApiOrder.exportOrders(this.pluginConfig).then((function(t){t.success?(e.isSaveSuccessful=!0,e.createNotificationSuccess({title:e.$tc("profitmetrics-api.order.title"),message:e.$tc("profitmetrics-api.order.success")})):e.createNotificationError({title:e.$tc("profitmetrics-api.order.title"),message:e.$tc("profitmetrics-api.order.error")})})).finally((function(){return e.isLoading=!1}))}}});var c=Shopware,s=c.Component,a=c.Mixin;s.register("profitmetrics-api-test-button",{template:'<div>\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        variant="primary"\n        @process-finish="saveFinish"\n        @click="testApi"\n        style="margin-top:20px;margin-bottom: 20px;"\n    >{{ $tc(\'profitmetrics.api-test.label\') }}</sw-button-process>\n</div>\n',inject:["profitMetricsApiService"],mixins:[a.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){var e=null,t=this;do{e=(t=t.$parent).actualConfigData}while(!e);return e[t.currentSalesChannelId]}},methods:{saveFinish:function(){this.isSaveSuccessful=!1},testApi:function(){var e=this;this.isLoading=!0,this.profitMetricsApiService.testConfig(this.pluginConfig).then((function(t){t.isValid?(e.isSaveSuccessful=!0,e.createNotificationSuccess({title:e.$tc("profitmetrics-api.test.title"),message:e.$tc("profitmetrics-api.test.success")})):e.createNotificationError({title:e.$tc("profitmetrics-api.test.title"),message:e.$tc("profitmetrics-api.test.error")})})).catch((function(){e.createNotificationError({title:e.$tc("profitmetrics-api.test.title"),message:e.$tc("profitmetrics-api.test.error")})})).finally((function(){e.isLoading=!1}))}}});r("ZxjN");var u=Shopware,p=u.Component,l=u.Mixin;p.register("profitmetrics-download-customers-button",{template:'<div>\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        variant="primary"\n        style="margin-top:20px"\n        @process-finish="saveFinish"\n        @click="downloadCustomers"\n    >{{ $tc(\'profitmetrics.customer-download-button.label\') }}</sw-button-process>\n</div>\n',inject:["profitMetricsDownloadCustomersService"],mixins:[l.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){var e=null,t=this;do{e=(t=t.$parent).actualConfigData}while(!e);return e[t.currentSalesChannelId]}},methods:{saveFinish:function(){this.isSaveSuccessful=!1},downloadCustomers:function(){var e=this;this.isLoading=!0,this.profitMetricsDownloadCustomersService.downloadCustomers(this.pluginConfig).then((function(e){var t=new Blob([e],{type:"application/csv"}),r=document.createElement("a");r.href=window.URL.createObjectURL(t);var n=new Date;r.download="pm_customers_"+n.toLocaleDateString("sv").replace(/-/g,"")+"_"+n.toTimeString().slice(0,8).replace(/:/g,"")+".csv",document.body.appendChild(r),r.click(),document.body.removeChild(r)})).catch((function(){e.createNotificationError({title:e.$tc("profitmetrics-api.test.title"),message:e.$tc("profitmetrics-api.test.error")})})).finally((function(){e.isLoading=!1}))}}});Shopware.Service("exportTemplateService").registerProductExportTemplate({name:"profitMetrics",translationKey:"sw-sales-channel.detail.productComparison.templates.template-label.profitMetrics",headerTemplate:'<?xml version="1.0" encoding="UTF-8" ?>\n<rss xmlns:g="http://base.google.com/ns/1.0" xmlns:pm="https://my.profitmetrics.io/ns/1.0">\n    <channel>\n'.trim(),bodyTemplate:"<item>\n    {# @var product \\Shopware\\Core\\Content\\Product\\SalesChannel\\SalesChannelProductEntity #}\n    <g:id>{{ product.productNumber }}</g:id>\n    <title>{{ product.translated.name|escape }}</title>\n    <g:image_link>{% if product.cover and product.cover.media %}{{ product.cover.media.url }}{% endif %}</g:image_link>\n    <link>{{ seoUrl('frontend.detail.page', {'productId': product.id}) }}</link>\n\n    {% set price = product.price.getCurrencyPrice(context.currencyId) %}\n    {% if product.price.getCurrencyPrice(context.currencyId).listPrice != null %}\n        {% set price = product.price.getCurrencyPrice(context.currencyId).listPrice %}\n    {% endif %}\n    {% set price = price.gross %}\n    <g:price>{{ price|number_format(context.currency.totalRounding.decimals, '.', '') }}</g:price>\n    <pm:price_currency>{{ context.currency.isoCode }}</pm:price_currency>\n\n    {# @var purchasePrice \\Shopware\\Core\\Framework\\DataAbstractionLayer\\Pricing\\Price #}\n    {% set purchasePrice = null %}\n    {% if product.purchasePrices %}\n        {% set purchasePrice = product.purchasePrices.currencyPrice(context.currencyId) %}\n    {% endif %}\n    <pm:price_buy>{%- if purchasePrice -%}{{ purchasePrice.net }}{%- endif -%}</pm:price_buy>\n    <pm:price_buy_currency>{{ context.currency.isoCode }}</pm:price_buy_currency>\n\n    <pm:num_stock>{{ product.availableStock }}</pm:num_stock>\n    <tax_state>{{ context.taxState }}</tax_state>\n</item>\n",footerTemplate:"</channel>\n</rss>\n".trim(),fileName:"profitMetrics.xml",encoding:"UTF-8",fileFormat:"xml",includeVariants:!0,generateByCronjob:!1,interval:86400});var f=r("jj8M"),d=r("peZD"),m=r("bshm");Shopware.Locale.extend("de-DE",f),Shopware.Locale.extend("en-GB",d),Shopware.Locale.extend("da-DK",m)},"Id+x":function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(o=i.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===r(c)?c:String(c)),i)}var o,c}function o(e,t){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,p=Shopware.Application,l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(p,e);var t,r,s,a=c(p);function p(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,p),a.call(this,e,t,r)}return t=p,(r=[{key:"exportOrders",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.post("_action/".concat(this.getApiBasePath(),"/order-export"),e,{headers:t}).then((function(e){return u.handleResponse(e)}))}}])&&i(t.prototype,r),s&&i(t,s),Object.defineProperty(t,"prototype",{writable:!1}),p}(u);p.addServiceProvider("profitmetricsApiOrder",(function(e){var t=p.getContainer("init");return new l(t.httpClient,e.loginService)}))},ZxjN:function(e,t){var r=Shopware.Component,n=Shopware.Data,i=n.Criteria;n.EntityCollection;r.extend("order-status-multiselect","sw-entity-multi-id-select",{inheritAttrs:!1,inject:["repositoryFactory"],props:{entity:{type:String,required:!1,default:"state_machine"},criteria:{type:Object,required:!1,default:function(){var e=new i;return e.addAssociation("stateMachine"),e}},repository:{type:Object,required:!1,default:function(){return this.repositoryFactory.create("state_machine_state")}}}}),r.override("sw-entity-multi-select",{methods:{displayLabelProperty:function(e){if("order-status-multiselect"!==this.$parent.$attrs.componentName)return this.$super("displayLabelProperty",e);var t=this.getKey(e,"name")||this.getKey(e,"translated.name"),r=this.getKey(e.stateMachine,"name")||this.getKey(e.stateMachine,"translated.name");return"order_delivery.state"===e.stateMachine.technicalName&&"Order state"===e.stateMachine.name&&(r="Order delivery state"),r+" - "+t}}})},bshm:function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"profitmetrics-api.order.title","success":"profitmetrics-api.order.success","error":"profitmetrics-api.order.error"},"test":{"title":"Test API forbindelse","success":"Oprettelse af forbindelse testet med success","error":"Forbindelse kunne ikke oprettes. Sikre at konfigurationsdataen er korrekt"}},"profitmetrics":{"send-orders":{"label":"Send ordrer"},"api-test":{"label":"Test API-forbindelse"},"customer-download-button":{"label":"Download kunder"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},jj8M:function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"profitmetrics-api.order.title","success":"profitmetrics-api.order.success","error":"profitmetrics-api.order.error"},"test":{"title":"Test API connection","success":"Verbindung wurde erfolgreich getestet","error":"Verbindung konnte nicht hergestellt werden. Bitte prüfe die Zugangsdaten"}},"profitmetrics":{"send-orders":{"label":"Bestellungen senden"},"api-test":{"label":"API-Verbindung testen"},"customer-download-button":{"label":"Kunden herunterladen"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},peZD:function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"profitmetrics-api.order.title","success":"profitmetrics-api.order.success","error":"profitmetrics-api.order.error"},"test":{"title":"Test API connection","success":"Connection tested with success","error":"Connection could not be established."}},"profitmetrics":{"send-orders":{"label":"Send orders now"},"api-test":{"label":"Test API connection"},"customer-download-button":{"label":"Download customers"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},"q+va":function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(o=i.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===r(c)?c:String(c)),i)}var o,c}function o(e,t){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var i=a(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,p=Shopware.Application,l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(p,e);var t,r,s,a=c(p);function p(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,p),a.call(this,e,t,r)}return t=p,(r=[{key:"downloadCustomers",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.get("_action/".concat(this.getApiBasePath(),"/customers-export"),e,t).then((function(e){return u.handleResponse(e)}))}}])&&i(t.prototype,r),s&&i(t,s),Object.defineProperty(t,"prototype",{writable:!1}),p}(u);p.addServiceProvider("profitMetricsDownloadCustomersService",(function(e){var t=p.getContainer("init");return new l(t.httpClient,e.loginService)}))}});
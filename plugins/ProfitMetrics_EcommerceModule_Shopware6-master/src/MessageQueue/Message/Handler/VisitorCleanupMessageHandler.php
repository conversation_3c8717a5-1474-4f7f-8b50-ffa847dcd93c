<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\Message\Handler;

use Doctrine\DBAL\Connection;
use Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorDefinition;
use Profitmetrics\Profitmetrics\MessageQueue\Message\VisitorCleanupMessage;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Defaults;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

/**
 * Removes visitor tracking data older than configured retention period
 */
#[AsMessageHandler]
class VisitorCleanupMessageHandler
{
    public function __construct(
        private readonly Connection $connection,
        private readonly ErrorHandler $errorHandler
    ) {
    }

    /**
     * Processes a visitor cleanup message by removing visitor records older than the retention period
     */
    public function __invoke(VisitorCleanupMessage $message): void
    {
        try {
            $entryLifetimeSeconds = $message->getRetentionDays() * 86400;
            $deleteBefore = (new \DateTime(sprintf('- %s seconds', $entryLifetimeSeconds)))
                ->format(Defaults::STORAGE_DATE_TIME_FORMAT);

            $this->connection->executeStatement(
                'DELETE FROM `' . VisitorDefinition::ENTITY_NAME . '` WHERE `created_at` < :before',
                ['before' => $deleteBefore]
            );
        } catch (\Throwable $e) {
            $this->errorHandler->logException('Failed to cleanup visitor data', $e);
        }
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\Message;

/**
 * Message for triggering order data export.
 */
class OrderExportMessage
{
    public function __construct(
        private readonly string $orderId
    ) {
    }

    public function getOrderId(): string
    {
        return $this->orderId;
    }
}

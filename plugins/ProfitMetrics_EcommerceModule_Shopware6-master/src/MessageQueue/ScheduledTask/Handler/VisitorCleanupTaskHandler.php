<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\Handler;

use Profitmetrics\Profitmetrics\MessageQueue\Message\VisitorCleanupMessage;
use Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\VisitorCleanupTask;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskCollection;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskHandler;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

/**
 * Handles scheduled cleanup of visitor tracking data.
 */
#[AsMessageHandler(handles: VisitorCleanupTask::class)]
final class VisitorCleanupTaskHandler extends ScheduledTaskHandler
{
    /**
     * @param EntityRepository<ScheduledTaskCollection> $scheduledTaskRepository For managing scheduled tasks
     * @param MessageBusInterface $messageBus For dispatching the cleanup message
     * @param ErrorHandler $errorHandler For error logging
     */
    public function __construct(
        EntityRepository $scheduledTaskRepository,
        private readonly MessageBusInterface $messageBus,
        private readonly ErrorHandler $errorHandler
    ) {
        parent::__construct($scheduledTaskRepository);
    }

    /**
     * Dispatches cleanup message to remove old visitor records
     *
     * @throws \Exception|ExceptionInterface When message dispatch fails
     */
    public function run(): void
    {
        try {
            $this->messageBus->dispatch(new VisitorCleanupMessage());
        } catch (ExceptionInterface $e) { // Now correctly refers to Symfony's ExceptionInterface
            $this->errorHandler->logException('Failed to dispatch VisitorCleanupMessage', $e);

            throw $e; // Re-throw to mark task as failed
        }
    }
}

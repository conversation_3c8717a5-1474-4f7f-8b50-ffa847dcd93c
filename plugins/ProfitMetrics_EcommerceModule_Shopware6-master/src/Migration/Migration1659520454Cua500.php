<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1659520454Cua500 extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1_659_520_454;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        // Check added since this was changed directly in Migration1613699220, instead of a new migration
        // This plugin is active on multiple sites and wouldn't get the updated type unless they've reinstalled

        $val = $connection->fetchAssociative(
            <<<SQL
            SHOW COLUMNS FROM `profitmetrics_visitor` WHERE Field = 'cua' AND LOWER(Type) = 'varchar(255)';
        SQL
        );

        if (!$val) {
            return;
        }

        $connection->executeStatement('ALTER TABLE `profitmetrics_visitor` MODIFY cua VARCHAR(500);');
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}

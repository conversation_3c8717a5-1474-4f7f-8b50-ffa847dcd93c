<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Exception\TableNotFoundException;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Migration\MigrationStep;
use Shopware\Core\Framework\Uuid\Uuid;

class Migration1685624072OrderVersionId extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1_685_624_072;
    }

    /**
     * @throws Exception|\Throwable
     */
    public function update(Connection $connection): void
    {
        if (!$this->columnExists($connection, 'profitmetrics_visitor', 'order_version_id')) {
            $connection->executeStatement(
                <<<SQL
                    alter table profitmetrics_visitor
                        add order_version_id binary(16) null after order_id;
                    update `profitmetrics_visitor` set `order_version_id` = :versionId where `version_id` != :versionId;
            SQL,
                [
                    'versionId' => Uuid::fromHexToBytes(Defaults::LIVE_VERSION),
                ]
            );
        }

        $this->dropForeignKeyIfExists2($connection, 'profitmetrics_visitor', 'profitmetrics_visitor___fk');
        if ($this->indexExists($connection, 'profitmetrics_visitor', 'profitmetrics_visitor___fk')) {
            $connection->executeStatement(
                <<<SQL
                alter table profitmetrics_visitor
                    drop index profitmetrics_visitor___fk;
                SQL
            );
        }

        $connection->executeStatement(
            <<<SQL
            alter table `profitmetrics_visitor`
                add constraint `profitmetrics_visitor___fk`
                    foreign key (`order_id`, `order_version_id`)
                        references `order` (`id`, `version_id`)
                        on delete cascade;
            SQL
        );
    }

    public function updateDestructive(Connection $connection): void
    {
    }

    protected function dropForeignKeyIfExists2(Connection $connection, string $table, string $column): bool
    {
        $sql = \sprintf('ALTER TABLE `%s` DROP FOREIGN KEY `%s`', $table, $column);

        try {
            $connection->executeStatement($sql);
        } catch (\Throwable $e) {
            if ($e instanceof TableNotFoundException) {
                return false;
            }

            // fk does not exists
            if (str_contains($e->getMessage(), 'SQLSTATE[42000]')) {
                return false;
            }

            throw $e;
        }

        return true;
    }
}

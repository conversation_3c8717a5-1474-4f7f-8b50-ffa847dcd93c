<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1613699220 extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1_613_699_220;
    }

    public function update(Connection $connection): void
    {
        $query = <<<SQL
            CREATE TABLE IF NOT EXISTS `profitmetrics_visitor` (
                `id` BINARY(16) NOT NULL,
                `gacid` VARCHAR(255) NULL,
                `gacid_source` VARCHAR(255) NULL,
                `gclid` VARCHAR(255) NULL,
                `fbp` VARCHAR(255) NULL,
                `fbc` VARCHAR(255) NULL,
                `cua` VARCHAR(500) NULL,
                `cip` VARCHAR(20) NULL,
                `thrackspec` VARCHAR(255) NULL,
                `timestamp` INT(11) NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            SQL;
        $connection->executeStatement($query);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration**********AddMarketingAndStatisticFields extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return **********;
    }

    public function update(Connection $connection): void
    {
        $tableName = 'profitmetrics_visitor';
        $schemaManager = $connection->getSchemaManager();
        $tableDetails = $schemaManager->listTableDetails($tableName);

        if (!$tableDetails->hasColumn('cc_statistics')) {
            $connection->executeStatement(
                "
            ALTER TABLE $tableName
            ADD cc_statistics TINYINT NULL;
        "
            );
        }

        if (!$tableDetails->hasColumn('cc_marketing')) {
            $connection->executeStatement(
                "
            ALTER TABLE $tableName
            ADD cc_marketing TINYINT NULL;
        "
            );
        }
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}

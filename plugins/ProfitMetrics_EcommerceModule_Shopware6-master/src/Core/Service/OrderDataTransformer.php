<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Service;

use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Order\Aggregate\OrderLineItem\OrderLineItemEntity;
use Shopware\Core\Checkout\Order\OrderEntity;

/**
 * Transforms an OrderEntity into an array suitable for ProfitMetrics API.
 */
class OrderDataTransformer
{
    private LineItemTransformer $lineItemTransformer;

    private WeightCalculator $weightCalculator;

    public function __construct(LineItemTransformer $lineItemTransformer, WeightCalculator $weightCalculator)
    {
        $this->lineItemTransformer = $lineItemTransformer;
        $this->weightCalculator = $weightCalculator;
    }

    /**
     * Transforms an OrderEntity into an array suitable for ProfitMetrics API.
     *
     * @return array<string, mixed>
     */
    public function transform(OrderEntity $order): array
    {
        $itemsData = [];

        /** @var OrderLineItemEntity $item */
        foreach ($order->getLineItems()->filterByType(LineItem::PRODUCT_LINE_ITEM_TYPE) as $item) {
            $itemsData[] = $this->lineItemTransformer->transform($item);
        }

        /** @var OrderLineItemEntity $item */
        foreach ($order->getLineItems()->filterByType(LineItem::CUSTOM_LINE_ITEM_TYPE) as $item) {
            $itemsData[] = $this->lineItemTransformer->transform($item);
        }

        $weight = $this->weightCalculator->calculateTotalWeight($order);

        $delivery = $order->getDeliveries()?->first();
        $transaction = $order->getTransactions()?->first();

        $shippingCost = $this->calculateShippingCost($order);
        $priceTotalExVat = max(0, $order->getAmountNet());
        $priceTotalInclVat = max(0, $order->getAmountTotal());

        return [
            'id' => (string) $order->getOrderNumber(),
            'orderEmail' => $order->getOrderCustomer()?->getEmail() ?? '',
            'currency' => $order->getCurrency()?->getIsoCode() ?? '',
            'priceShippingExVat' => $shippingCost,
            'priceTotalExVat' => (float) sprintf('%.2f', $priceTotalExVat),
            'priceTotalInclVat' => (float) sprintf('%.2f', $priceTotalInclVat),
            'paymentMethod' => $transaction?->getPaymentMethod()?->getName() ?? '',
            'shippingMethod' => $delivery?->getShippingMethod()?->getName() ?? '',
            'shippingZipcode' => $delivery?->getShippingOrderAddress()?->getZipcode() ?? '',
            'shippingCountry' => $delivery?->getShippingOrderAddress()?->getCountry()?->getName() ?? '',
            'shippingWeight' => $weight,
            'products' => $itemsData,
            'ts' => $order->getOrderDateTime()->getTimestamp(),
        ];
    }

    /**
     * Calculates the shipping cost excluding VAT.
     */
    private function calculateShippingCost(OrderEntity $order): float
    {
        $shippingCosts = $order->getShippingCosts();
        $shippingCost = $shippingCosts->getUnitPrice();
        $orderPrice = $order->getPrice();

        if (!$orderPrice->hasNetPrices()) {
            $shippingTax = $shippingCosts->getCalculatedTaxes()->first();
            if ($shippingTax !== null) {
                $shippingCost = $shippingTax->getPrice() - $shippingTax->getTax();
            }
        }

        return $shippingCost;
    }
}

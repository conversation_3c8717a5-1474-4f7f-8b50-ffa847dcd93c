<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Api;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Checkout\Order\Aggregate\OrderCustomer\OrderCustomerDefinition;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controller for exporting customer data to a CSV file.
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
final class CustomerExportController
{
    /**
     * @var array<string>
     */
    private const CSV_HEADERS = ['emailSha256', 'firstOrderTimestamp'];
    /**
     * @var string
     */
    private const DATE_FORMAT = 'Y-m-d\TH:i:s\Z';

    /**
     * @var string
     */
    private const FILENAME = 'customers.csv';

    /**
     * @var int
     */
    private const CHUNK_SIZE = 1000;

    public function __construct(
        private readonly Connection $connection,
        private readonly ErrorHandler $errorHandler
    ) {
    }

    /**
     * Handles the export of customers to a CSV file.
     */
    #[Route(
        path: '/api/_action/profitmetrics-api/customers-export',
        name: 'profitmetrics.customers.export',
        methods: ['GET']
    )]
    public function exportCustomers(): Response
    {
        return new StreamedResponse(
            callback: $this->generateCustomerExport(...),
            status: Response::HTTP_OK,
            headers: [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => sprintf('attachment; filename="%s"', self::FILENAME),
            ]
        );
    }

    /**
     * Generates the customer export CSV by writing headers and customer data in chunks.
     *
     * @throws Exception When database operations fail
     * @throws \RuntimeException When output stream operations fail
     */
    private function generateCustomerExport(): void
    {
        $output = $this->openOutputStream();

        try {
            $this->writeCustomerData($output);
        } catch (\Exception $e) {
            $this->errorHandler->log(
                'Error during customer export',
                ['error' => $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );

            throw $e;
        } finally {
            fclose($output);
        }
    }

    /**
     * Opens the output stream for writing the CSV.
     */
    /**
     * @throws \RuntimeException
     *
     * @return resource
     */
    private function openOutputStream()
    {
        $output = fopen('php://output', 'wb');
        if ($output === false) {
            throw new \RuntimeException('Failed to open output stream for customer export');
        }

        return $output;
    }

    /**
     * Writes customer data to the CSV.
     *
     * @throws Exception
     */
    /**
     * @param resource $output
     *
     * @throws Exception
     */
    private function writeCustomerData($output): void
    {
        if (fputcsv($output, self::CSV_HEADERS) === false) {
            throw new \RuntimeException('Failed to write CSV headers');
        }

        $offset = 0;
        while ($customers = $this->fetchCustomerDataChunk($offset)) {
            foreach ($customers as $customer) {
                try {
                    if (fputcsv($output, [
                        $this->hashEmail($customer['email']),
                        $this->formatTimestamp($customer['created_at']),
                    ]) === false) {
                        $this->errorHandler->log(
                            'Failed to write customer data to CSV',
                            ['email_hash' => $this->hashEmail($customer['email'])],
                            Response::HTTP_INTERNAL_SERVER_ERROR
                        );
                    }
                } catch (\Throwable $e) {
                    $this->errorHandler->log(
                        'Error processing customer data',
                        [
                            'error' => $e->getMessage(),
                            'email_hash' => $this->hashEmail($customer['email']),
                        ],
                        Response::HTTP_INTERNAL_SERVER_ERROR
                    );

                    continue;
                }
            }
            $offset += self::CHUNK_SIZE;
        }
    }

    /**
     * Fetches a paginated chunk of customer data from the database.
     *
     * @param int $offset The starting offset for pagination
     *
     * @throws Exception When the database query fails
     *
     * @return array<int, array<string, string>> Array of customer data with email and created_at fields
     */
    /**
     * @throws Exception
     *
     * @return array<int, array<string, string>>
     */
    private function fetchCustomerDataChunk(int $offset): array
    {
        return $this->connection->createQueryBuilder()
            ->select('DISTINCT email, MIN(created_at) as created_at')
            ->from(OrderCustomerDefinition::ENTITY_NAME)
            ->groupBy('email')
            ->orderBy('created_at', 'ASC')
            ->setMaxResults(self::CHUNK_SIZE)
            ->setFirstResult($offset)
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * Hashes the customer's email using SHA-256.
     */
    private function hashEmail(string $email): string
    {
        return hash('sha256', strtolower(trim($email)));
    }

    /**
     * Formats a timestamp to the specified date format.
     */
    private function formatTimestamp(string $timestamp): string
    {
        try {
            $dateTime = new \DateTimeImmutable($timestamp);

            return $dateTime->format(self::DATE_FORMAT);
        } catch (\Exception $e) {
            $this->errorHandler->log(
                'Invalid timestamp format',
                [
                    'timestamp' => $timestamp,
                    'expected_format' => self::DATE_FORMAT,
                    'error' => $e->getMessage(),
                ],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );

            throw new \RuntimeException(
                sprintf('Invalid timestamp format: %s', $timestamp),
                Response::HTTP_INTERNAL_SERVER_ERROR,
                $e
            );
        }
    }
}

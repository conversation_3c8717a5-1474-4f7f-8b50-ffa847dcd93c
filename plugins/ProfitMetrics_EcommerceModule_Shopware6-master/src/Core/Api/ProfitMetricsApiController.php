<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Api;

use Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;

#[Route(defaults: ['_routeScope' => ['administration']])]
final class ProfitMetricsApiController
{
    public function __construct(
        private readonly ProfitMetricsApiClient $profitMetricsClient,
        private readonly ErrorHandler $errorHandler
    ) {
    }

    /**
     * Verifies ProfitMetrics API credentials by checking the provided ID.
     */
    #[Route(path: '/api/_action/profitmetrics-api/verify', name: 'api.action.profitmetrics.verify', defaults: ['_httpCache' => false, 'XmlHttpRequest' => 'true', 'auth_required' => false], methods: ['POST'])]
    public function verifyCredentials(RequestDataBag $dataBag): JsonResponse
    {
        $profitMetricsId = $dataBag->get(ConfigDefaults::CONFIG_PROFIT_METRICS_ID);

        if (empty($profitMetricsId)) {
            $this->errorHandler->log(
                'ProfitMetrics ID is missing',
                ['profitMetricsId' => null],
                400
            );

            return $this->createErrorResponse('ProfitMetrics ID is required');
        }

        try {
            $request = $this->profitMetricsClient->getRequest(['pid' => $profitMetricsId]);
            if (!$request->success) {
                $this->errorHandler->log(
                    'Invalid ProfitMetrics ID',
                    ['profitMetricsId' => $profitMetricsId],
                    400
                );

                return $this->createErrorResponse('API verification failed');
            }

            return new JsonResponse(['isValid' => true]);
        } catch (ExceptionInterface $error) {
            $this->errorHandler->logException(
                'An unknown error occurred while verifying ProfitMetrics API credentials',
                $error
            );

            return $this->createErrorResponse(
                'API verification failed: ' . $error->getMessage(),
                500
            );
        }
    }

    /**
     * Creates a standardized error response.
     */
    private function createErrorResponse(string $message, int $statusCode = 400): JsonResponse
    {
        return new JsonResponse([
            'isValid' => false,
            'message' => $message,
        ], $statusCode);
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Command;

use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults;
use Profitmetrics\Profitmetrics\MessageQueue\Message\OrderExportMessage;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

/**
 * This command exists alongside ScheduledTask for manual execution with additional
 * debug/control options (sales channel filtering, dry-run, visual output, limits)
 * not needed in automated tasks.
 */
#[AsCommand(
    name: 'profitmetrics:order:export',
    description: 'Manually export orders to ProfitMetrics with optional filtering and debug options'
)]
class OrderExportCommand extends Command
{
    /**
     * @param EntityRepository<OrderCollection> $orderRepository
     */
    public function __construct(
        private readonly ErrorHandler $errorHandler,
        private readonly OrderExportCriteriaBuilder $orderCriteriaService,
        private readonly EntityRepository $orderRepository,
        private readonly MessageBusInterface $messageBus
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addOption(
            'salesChannelId',
            's',
            InputOption::VALUE_OPTIONAL,
            'Specify sales channel ID for order export'
        );

        $this->addOption(
            'dry-run',
            'd',
            InputOption::VALUE_NONE,
            'Skips order export and only displays orders set to be exported'
        );
        $this->addOption(
            'visual',
            'visual',
            InputOption::VALUE_NONE,
            'Display table of orders set to be exported'
        );

        $this->addOption(
            'limit',
            'l',
            InputOption::VALUE_REQUIRED,
            'Limit the number of orders to process'
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $context = new Context(new SystemSource());

        try {
            $criteria = $this->orderCriteriaService->createExportCriteria($context, false);

            if ($input->getOption('salesChannelId')) {
                $criteria->addFilter(new EqualsFilter('salesChannelId', $input->getOption('salesChannelId')));
            }

            if ($input->getOption('limit') !== null) {
                $criteria->setLimit((int) $input->getOption('limit'));
            }

            $orders = $this->orderRepository->search($criteria, $context);
            $orderIds = $orders->getIds();

            $output->writeln(
                \count($orderIds)
                    ? sprintf('%d order(s) set to be exported', \count($orderIds))
                    : 'No orders should be exported'
            );

            if (!$input->getOption('dry-run') && !empty($orderIds)) {
                // Mark orders as in progress
                $updates = array_map(
                    fn (string $id) => [
                        'id' => $id,
                        'customFields' => [
                            CustomFieldDefaults::PM_ORDER_EXPORT_STATE => CustomFieldDefaults::EXPORT_STATE_IN_PROGRESS,
                        ],
                    ],
                    $orderIds
                );

                $this->orderRepository->update($updates, $context);

                // Dispatch export messages
                foreach ($orderIds as $orderId) {
                    $this->messageBus->dispatch(new OrderExportMessage($orderId));
                }
            }

            if ($input->getOption('visual')) {
                $table = new Table($output);
                $table->setHeaders([
                    'orderId' => 'Order ID',
                    'orderNumber' => 'Order Number',
                    'orderTime' => 'Order Time',
                    'salesChannelId' => 'Sales Channel ID',
                    'attempts' => 'Export Attempts',
                    'amountTotal' => 'Total Amount',
                    'isoCode' => 'ISO Code',
                    'taxStatus' => 'Tax Status',
                    'visitorId' => 'Visitor ID',
                ]);

                $attempt = $input->getOption('dry-run') ? 0 : 1;
                $table->setRows($orders->map(fn (OrderEntity $order) => [
                    'orderId' => $order->getId(),
                    'orderNumber' => $order->getOrderNumber(),
                    'orderTime' => $order->getOrderDateTime()->format(Defaults::STORAGE_DATE_TIME_FORMAT),
                    'salesChannelId' => $order->getSalesChannelId(),
                    'attempts' => (($order->getCustomFields()['profitmetrics_export_attempts'] ?? 0) + $attempt),
                    'amountTotal' => $order->getAmountTotal(),
                    'isoCode' => $order->getCurrency()?->getIsoCode() ?: '',
                    /** @phpstan-ignore-next-line */
                    'taxStatus' => $order->getTaxStatus() ?? '',
                    'visitorId' => $order->getCustomFields()[CustomFieldDefaults::PM_ORDER_VISITOR_ID] ?? '',
                ]));

                $table->setFooterTitle("{$orders->getTotal()} order(s) set to be exported");

                $table->render();
            }
        } catch (\Throwable $exception) {
            $output->writeln('Error occurred during order export:');
            $output->writeln(sprintf('  Message: %s', $exception->getMessage()));

            $this->errorHandler
                ->logException('profitmetrics.command.exception', $exception, $input->getOptions())
                ->clear();

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}

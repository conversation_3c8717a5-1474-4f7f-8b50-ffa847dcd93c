<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Command;

use Doctrine\DBAL\Connection;
use Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorDefinition;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Defaults;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * This command exists alongside ScheduledTask for manual execution with additional
 * debug/control options (configurable entry lifetime) not needed in automated tasks.
 */
#[AsCommand(
    name: 'profitmetrics:visitor:cleanup',
    description: 'Cleanup old visitor tracking data with configurable retention period'
)]
class VisitorCleanupCommand extends Command
{
    /**
     * Default retention period of 90 days in seconds
     */
    private const DEFAULT_ENTRY_LIFETIME_SECONDS = 7_776_000;

    public function __construct(
        protected Connection $connection,
        protected ErrorHandler $errorHandler
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addOption(
            'entry-lifetime-seconds',
            't',
            InputOption::VALUE_OPTIONAL,
            'Maximum retention period for visitor entries in seconds (default: 90 days)',
            self::DEFAULT_ENTRY_LIFETIME_SECONDS
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $entryLifetimeSeconds = $input->getOption('entry-lifetime-seconds');

        try {
            $deleteBefore = (new \DateTime(sprintf('- %s seconds', $entryLifetimeSeconds)))
                ->format(Defaults::STORAGE_DATE_TIME_FORMAT);
            $rowCount = $this->connection->executeStatement(
                'DELETE FROM `' . VisitorDefinition::ENTITY_NAME . '` WHERE `created_at` < :before',
                ['before' => $deleteBefore]
            );
            $output->writeln(sprintf('Cleanup successful, removed %s row(s)', $rowCount));

            return Command::SUCCESS;
        } catch (\InvalidArgumentException $e) {
            $output->writeln('Invalid argument provided for cleanup operation');

            return Command::FAILURE;
        } catch (\Throwable $e) {
            $output->writeln('Error occurred during visitor cleanup');

            return Command::FAILURE;
        }
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Service;

use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Shopware\Core\Checkout\Order\Aggregate\OrderTransaction\OrderTransactionStates;
use Shopware\Core\Checkout\Order\OrderStates;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NorFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\OrFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\RangeFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateCollection;
use Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;

/**
 * Builds search criteria for finding orders eligible for export
 */
class OrderExportCriteriaBuilder
{
    public function __construct(
        /** @var EntityRepository<StateMachineStateCollection> */
        private readonly EntityRepository $stateRepository,
        private readonly SystemConfigService $systemConfigService,
    ) {
    }

    /**
     * Creates criteria for finding orders that need to be exported.
     */
    public function createExportCriteria(Context $context, bool $includeExportState = true): Criteria
    {
        $criteria = new Criteria();
        $filters = [...$this->createStatusFilters($context)->getFilters()];

        if ($includeExportState) {
            // Select orders that haven't been exported or marked for export
            $filters[] = new OrFilter([
                new EqualsFilter('customFields.profitmetrics_export_state', null),
                new EqualsFilter('customFields.profitmetrics_export_state', 0),
            ]);
        }

        // Exclude cancelled orders
        $filters[] = new NorFilter([
            new EqualsFilter('stateMachineState.technicalName', OrderStates::STATE_CANCELLED),
        ]);

        // Time Range Filter
        $maxOrderAge = $this->systemConfigService->getInt(ConfigDefaults::CONFIG_MAX_ORDER_AGE) ?: 7;
        $dateFrom = new \DateTime("-$maxOrderAge days");
        $filters[] = new RangeFilter('orderDate', [
            RangeFilter::GTE => $dateFrom->format(Defaults::STORAGE_DATE_TIME_FORMAT),
        ]);

        // Apply all filters
        $criteria->addFilter(new MultiFilter(MultiFilter::CONNECTION_AND, $filters));

        // Add sorting to ensure consistent order processing
        $criteria->addSorting(new FieldSorting('orderDate', FieldSorting::ASCENDING));
        $criteria->addSorting(new FieldSorting('id', FieldSorting::ASCENDING));

        return $criteria;
    }

    /**
     * Creates the status filters for order selection.
     */
    private function createStatusFilters(Context $context): Criteria
    {
        $configuredStatuses = $this->systemConfigService->get(ConfigDefaults::CONFIG_ORDER_STATUSES);

        if (\is_array($configuredStatuses) && !empty($configuredStatuses)) {
            $criteria = new Criteria();
            $criteria->addFilter(
                new MultiFilter(
                    MultiFilter::CONNECTION_AND,
                    [
                        // Either order state OR transaction state should match configured statuses
                        new OrFilter([
                            // Order state matches and is not null
                            new MultiFilter(
                                MultiFilter::CONNECTION_AND,
                                [
                                    new EqualsAnyFilter('stateMachineState.id', $configuredStatuses),
                                    new NotFilter(MultiFilter::CONNECTION_AND, [
                                        new EqualsFilter('stateMachineState.id', null),
                                    ]),
                                ]
                            ),
                            // Transaction state matches and is not null
                            new MultiFilter(
                                MultiFilter::CONNECTION_AND,
                                [
                                    new EqualsAnyFilter('transactions.stateMachineState.id', $configuredStatuses),
                                    new NotFilter(MultiFilter::CONNECTION_AND, [
                                        new EqualsFilter('transactions.stateMachineState.id', null),
                                    ]),
                                ]
                            ),
                        ]),
                    ]
                )
            );

            return $criteria;
        }

        // Default status logic when no specific statuses are configured
        $statusCriteria = new Criteria();
        $defaultStates = [];

        // Get completed order state
        $orderCompletedCriteria = clone $statusCriteria;
        $orderCompletedCriteria->addFilter(
            new MultiFilter(
                MultiFilter::CONNECTION_AND,
                [
                    new EqualsFilter('technicalName', OrderStates::STATE_COMPLETED),
                    new EqualsFilter('stateMachine.technicalName', 'order.state'),
                    new NotFilter(MultiFilter::CONNECTION_AND, [
                        new EqualsFilter('id', null),
                    ]),
                ]
            )
        );

        /** @var StateMachineStateEntity|null $completedState */
        $completedState = $this->stateRepository->search($orderCompletedCriteria, $context)->first();
        if ($completedState) {
            $defaultStates[] = $completedState->getId();
        }

        // Get paid transaction state
        $transactionPaidCriteria = clone $statusCriteria;
        $transactionPaidCriteria->addFilter(
            new MultiFilter(
                MultiFilter::CONNECTION_AND,
                [
                    new EqualsFilter('technicalName', OrderTransactionStates::STATE_PAID),
                    new EqualsFilter('stateMachine.technicalName', 'order_transaction.state'),
                    new NotFilter(MultiFilter::CONNECTION_AND, [
                        new EqualsFilter('id', null),
                    ]),
                ]
            )
        );

        /** @var StateMachineStateEntity|null $paidState */
        $paidState = $this->stateRepository->search($transactionPaidCriteria, $context)->first();
        if ($paidState) {
            $defaultStates[] = $paidState->getId();
        }

        $criteria = new Criteria();
        if (empty($defaultStates)) {
            // Fallback to basic paid transaction filter if states not found
            $criteria->addFilter(
                new MultiFilter(
                    MultiFilter::CONNECTION_AND,
                    [
                        new EqualsFilter(
                            'transactions.stateMachineState.technicalName',
                            OrderTransactionStates::STATE_PAID
                        ),
                        new NotFilter(MultiFilter::CONNECTION_AND, [
                            new EqualsFilter('transactions.stateMachineState.id', null),
                        ]),
                    ]
                )
            );

            return $criteria;
        }

        // Return orders where either state matches configured states
        $criteria->addFilter(
            new OrFilter([
                // Order state matches and is not null
                new MultiFilter(
                    MultiFilter::CONNECTION_AND,
                    [
                        new EqualsAnyFilter('stateMachineState.id', $defaultStates),
                        new NotFilter(MultiFilter::CONNECTION_AND, [
                            new EqualsFilter('stateMachineState.id', null),
                        ]),
                    ]
                ),
                // Transaction state matches and is not null
                new MultiFilter(
                    MultiFilter::CONNECTION_AND,
                    [
                        new EqualsAnyFilter('transactions.stateMachineState.id', $defaultStates),
                        new NotFilter(MultiFilter::CONNECTION_AND, [
                            new EqualsFilter('transactions.stateMachineState.id', null),
                        ]),
                    ]
                ),
            ])
        );

        return $criteria;
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Service;

use Monolog\Level;
use Monolog\Logger;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;

/**
 * Handles error logging and persistence for the ProfitMetrics plugin
 */
final class ErrorHandler
{
    protected const LOG_CHANNEL = 'profitmetrics';

    private ?string $message = null;

    private ?int $level = null;

    /**
     * @var array<string, mixed>
     */
    private array $logContext = [];

    /**
     * @param EntityRepository<EntityCollection<Entity>> $logEntryRepository
     */
    public function __construct(
        private readonly Logger $logger,
        private readonly EntityRepository $logEntryRepository
    ) {
    }

    /**
     * Logs an exception with additional context
     *
     * @param string $message Error message
     * @param \Throwable $exception The exception to log
     * @param array<string, mixed> $context Additional context
     */
    public function logException(string $message, \Throwable $exception, array $context = []): self
    {
        $exceptionContext = [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'error' => $exception->getMessage(),
            'errorType' => $exception::class,
        ];

        return $this->log(
            $message,
            array_merge($exceptionContext, $context),
            $exception->getCode()
        );
    }

    /**
     * Logs a message with context at specified level
     *
     * @param string $message Log message
     * @param array<string, mixed> $context Log context
     * @param int|null $code Error code/level
     */
    public function log(string $message, array $context, ?int $code = null): self
    {
        $this->message = $message;
        $this->logContext = $context;
        $this->level = $this->normalizeLogLevel($code ?? Level::Error->value);

        $this->logger->addRecord(Level::from($this->level), $this->message, $this->logContext);

        return $this;
    }

    /**
     * Persists the current log data to storage
     */
    public function addToLogEntry(Context $context): self
    {
        if (!$this->hasValidLogData()) {
            return $this;
        }

        $this->logEntryRepository->create([
            [
                'message' => $this->message,
                'context' => $this->logContext,
                'level' => $this->level,
                'channel' => self::LOG_CHANNEL,
            ],
        ], $context);

        return $this;
    }

    public function clear(): void
    {
        $this->message = null;
        $this->level = null;
        $this->logContext = [];
    }

    /**
     * Normalizes error codes into valid log levels
     *
     * @param int $code Raw error/level code
     *
     * @return int Normalized Monolog level
     */
    private function normalizeLogLevel(int $code): int
    {
        if (\in_array($code, Level::VALUES, true)) {
            return $code;
        }

        return (int) substr((string) $code, 0, 1) * 100;
    }

    /**
     * Checks if there is valid log data to persist
     */
    private function hasValidLogData(): bool
    {
        return $this->message !== null && !empty($this->logContext);
    }
}

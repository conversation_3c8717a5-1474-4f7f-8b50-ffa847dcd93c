<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Service;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\OrderExportTask;
use Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\VisitorCleanupTask;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskCollection;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskDefinition;
use Shopware\Core\System\CustomField\Aggregate\CustomFieldSet\CustomFieldSetCollection;
use Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateCollection;
use Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Service for managing plugin installation, updates, and uninstallation.
 *
 * This service handles the initialization and cleanup of plugin-specific
 * configurations, custom fields, and system settings during the plugin lifecycle.
 */
final class PluginInstallationService
{
    private readonly CustomFieldService $customFieldService;

    private readonly SystemConfigService $systemConfigService;

    /**
     * @var EntityRepository<ScheduledTaskCollection>
     */
    private readonly EntityRepository $scheduledTaskRepository;

    /**
     * @var EntityRepository<StateMachineStateCollection>
     */
    private readonly EntityRepository $stateRepository;

    public function __construct(ContainerInterface $container)
    {
        // Repositories
        /** @var EntityRepository<CustomFieldSetCollection> $customFieldSetRepository */
        $customFieldSetRepository = $container->get('custom_field_set.repository');
        /** @var EntityRepository<EntityCollection<Entity>> $customFieldRepository */
        $customFieldRepository = $container->get('custom_field.repository');
        /** @var EntityRepository<ScheduledTaskCollection> $scheduledTaskRepository */
        $scheduledTaskRepository = $container->get('scheduled_task.repository');
        /** @var EntityRepository<StateMachineStateCollection> $stateRepository */
        $stateRepository = $container->get('state_machine_state.repository');

        /** @var Connection $connection */
        $connection = $container->get(Connection::class);
        /** @var SystemConfigService $systemConfigService */
        $systemConfigService = $container->get(SystemConfigService::class);

        $this->customFieldService = new CustomFieldService(
            $customFieldSetRepository,
            $customFieldRepository,
            $connection
        );
        $this->systemConfigService = $systemConfigService;
        $this->scheduledTaskRepository = $scheduledTaskRepository;
        $this->stateRepository = $stateRepository;
    }

    /**
     * Setup custom fields during plugin installation or update.
     */
    public function installCustomFields(Context $context): void
    {
        $this->customFieldService->createCustomFields($context);
    }

    /**
     * Remove all plugin-specific custom fields during uninstallation.
     *
     * @throws Exception
     */
    public function uninstallCustomFields(Context $context): void
    {
        $this->customFieldService->removeCustomFields($context);
    }

    /**
     * Initialize or update plugin configuration settings.
     */
    public function updateConfig(Context $context): void
    {
        /** @var array<string>|null $previousConfigVal */
        $previousConfigVal = $this->systemConfigService->get(ConfigDefaults::CONFIG_ORDER_STATUSES);
        if ($previousConfigVal === null) {
            $criteria = new Criteria();
            $criteria->addAssociation('stateMachine');

            /** @var StateMachineStateCollection $states */
            $states = $this->stateRepository->search($criteria, $context)->getEntities();

            $defaultStates = [];
            /** @var StateMachineStateEntity $state */
            foreach ($states as $state) {
                if (
                    ($state->getTechnicalName() === 'completed' && $state->getStateMachine()->getTechnicalName() === 'order.state')
                    || ($state->getTechnicalName() === 'paid' && $state->getStateMachine()->getTechnicalName() === 'order_transaction.state')
                ) {
                    $defaultStates[] = $state->getId();
                }
            }
            $this->systemConfigService->set(ConfigDefaults::CONFIG_ORDER_STATUSES, $defaultStates);
        }

        $previousConfigVal = $this->systemConfigService->get(ConfigDefaults::CONFIG_ACTIVE);
        if ($previousConfigVal === null) {
            $this->systemConfigService->set(ConfigDefaults::CONFIG_ACTIVE, true);
        }

        $previousHeadlessConfigVal = $this->systemConfigService->get(ConfigDefaults::CONFIG_HEADLESS_MODE);
        if ($previousHeadlessConfigVal === null) {
            $this->systemConfigService->set(ConfigDefaults::CONFIG_HEADLESS_MODE, false);
        }
    }

    /**
     * Install scheduled tasks required by the plugin.
     *
     * @throws \Exception
     */
    public function installScheduledTasks(Context $context): void
    {
        try {
            $taskClasses = [
                OrderExportTask::class,
                VisitorCleanupTask::class,
            ];

            // Remove existing tasks
            $criteria = new Criteria();
            $criteria->addFilter(
                new MultiFilter(
                    MultiFilter::CONNECTION_OR,
                    array_map(
                        fn (string $class) => new EqualsFilter('scheduledTaskClass', $class),
                        $taskClasses
                    )
                )
            );

            $existingTasks = $this->scheduledTaskRepository->searchIds($criteria, $context)->getIds();
            if (!empty($existingTasks)) {
                $this->scheduledTaskRepository->delete(
                    array_map(fn ($id) => ['id' => $id], $existingTasks),
                    $context
                );
            }

            // Create new tasks
            $tasks = array_map(
                function (string $class) {
                    return [
                        'name' => $class::TASK_NAME,
                        'scheduledTaskClass' => $class,
                        'runInterval' => $class::TASK_INTERVAL,
                        'defaultRunInterval' => $class::TASK_INTERVAL,
                        'status' => ScheduledTaskDefinition::STATUS_SCHEDULED,
                        'nextExecutionTime' => (new \DateTime())->modify(sprintf('+%d seconds', $class::TASK_INTERVAL)),
                        'lastExecutionTime' => null,
                    ];
                },
                $taskClasses
            );

            $this->scheduledTaskRepository->create($tasks, $context);
        } catch (\Exception $e) {
            throw new \Exception(
                sprintf('Failed to install scheduled tasks: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Remove all plugin-specific scheduled tasks during uninstallation.
     *
     * @throws \Exception
     */
    public function uninstallScheduledTasks(Context $context): void
    {
        try {
            $criteria = new Criteria();
            $criteria->addFilter(
                new MultiFilter(
                    MultiFilter::CONNECTION_OR,
                    [
                        new EqualsFilter('scheduledTaskClass', OrderExportTask::class),
                        new EqualsFilter('scheduledTaskClass', VisitorCleanupTask::class),
                    ]
                )
            );

            $tasks = $this->scheduledTaskRepository->searchIds($criteria, $context)->getIds();

            if (!empty($tasks)) {
                $this->scheduledTaskRepository->delete(
                    array_map(fn ($id) => ['id' => $id], $tasks),
                    $context
                );
            }
        } catch (\Exception $e) {
            throw new \Exception(
                sprintf('Failed to remove scheduled tasks: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}

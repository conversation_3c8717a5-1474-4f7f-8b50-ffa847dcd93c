<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Defaults;

/**
 * Defines constants for custom field identifiers.
 */
final class CustomFieldDefaults
{
    /**
     * Custom field set identifiers
     */
    public const PM_FIELD_SET_PRODUCT = 'profitmetrics_product';
    public const PM_FIELD_SET_ORDER = 'profitmetrics_order';

    /**
     * Product field identifiers
     */
    public const PM_PRODUCT_BUY_PRICE = 'profitmetrics_buy_price';

    /**
     * Order field identifiers - Core tracking
     */
    public const PM_ORDER_GOOGLE_ADS_SCRIPT = 'profitmetrics_google_ads_script';
    public const PM_ORDER_VISITOR_ID = 'profitmetrics_visitor_id';
    public const PM_ORDER_SENT_DATE = 'profitmetrics_sent_date';
    public const PM_ORDER_EXPORT_STATE = 'profitmetrics_export_state';
    public const PM_ORDER_RESPONSE = 'profitmetrics_response';

    /**
     * Order export state enum
     */
    public const EXPORT_STATE_FAILED = -1;
    public const EXPORT_STATE_PENDING = 0;
    public const EXPORT_STATE_IN_PROGRESS = 1;
    public const EXPORT_STATE_COMPLETED = 2;
}

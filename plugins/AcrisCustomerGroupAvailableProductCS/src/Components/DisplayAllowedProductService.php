<?php declare(strict_types=1);

namespace Acris\CustomerGroupAvailableProduct\Components;

use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;

class DisplayAllowedProductService
{
    public const DEFAULT_PLUGIN_CONFIG_RELEASE_PRODUCT_IF_NO_CUSTOMER_GROUPS_ASSIGNED = 'unlockIfEmpty';

    /**
     * @var EntityRepository
     */
    private $productCustomerGroupRepository;
    /**
     * @var EntityRepository
     */
    private $productRepository;
    /**
     * @var EntityRepository
     */
    private $categoryRepository;
    /**
     * @var array
     */
    private $displayProductIds;


    public function __construct(
        EntityRepository $productCustomerGroupRepository,
        EntityRepository $productRepository,
        EntityRepository $categoryRepository
    )
    {
        $this->productCustomerGroupRepository = $productCustomerGroupRepository;
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->displayProductIds = [];
    }

    public function getDisplayProductIdsForCustomerGroupId(?string $customerGroupId, Context $context): array
    {
        if(empty($customerGroupId)) {
            return [];
        }

        if(array_key_exists($customerGroupId, $this->displayProductIds) && $this->displayProductIds[$customerGroupId]) {
            return $this->displayProductIds[$customerGroupId];
        }

        /** @var IdSearchResult $displayProductIdsResult */
        $displayProductIdsResult = $this->productCustomerGroupRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('customerGroupId', $customerGroupId)), $context);

        if(empty($displayProductIdsResult->getIds())) {
            return [];
        }

        $displayProductIds = [];
        foreach ($displayProductIdsResult->getIds() as $customerGroupProduct) {
            array_push($displayProductIds, $customerGroupProduct['productId']);
        }

        $displayProductIds = $this->getVariantIds($displayProductIds, $context);
        $this->displayProductIds[$customerGroupId] = $displayProductIds;

        return $displayProductIds;
    }

    public function checkIfNoCustomerGroupsAssigned(?string $productId, Context $context): bool
    {
        if(empty($productId)) {
            return false;
        }

        /** @var IdSearchResult $productIdSearch */
        $productIdSearch = $this->productCustomerGroupRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('productId', $productId)), $context);

        return $productIdSearch->getTotal() === 0;
    }

    public function getVariantIds(array $productIds, Context $context): array
    {
        if (empty($productIds)) return [];
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('parentId', $productIds));
        /** @var IdSearchResult $variantIds */
        $variantIds = $this->productRepository->searchIds($criteria, $context);
        if ($variantIds->getTotal() <= 0) return $productIds;
        foreach ($variantIds->getIds() as $productId) {
            array_push($productIds, $productId);
        }

        return $productIds;
    }

    public function getCategoryIdsForCacheInvalidate(Context $context): array
    {
        $categoryIds = $this->categoryRepository->searchIds((new Criteria()), $context)->getIds();

        return (!empty($categoryIds)) ? $categoryIds : [];
    }
}

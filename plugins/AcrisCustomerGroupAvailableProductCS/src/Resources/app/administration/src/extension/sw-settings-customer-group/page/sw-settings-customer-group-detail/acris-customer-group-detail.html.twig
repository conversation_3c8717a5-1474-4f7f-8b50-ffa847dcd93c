{% block sw_settings_customer_group_detail_content_card %}
    {% parent %}

    {% block acris_customer_group_available_product_detail_content_card %}
        <sw-card :isLoading="isLoading" v-if="customerGroup && customerGroup.extensions"
                 :title="$tc('acris-customer-group-available-product.availableProductsCard')">

            {% block acris_customer_group_available_product_many_to_many_select %}
                <acris-entity-many-to-many-select
                    :localMode="true"
                    :label="$tc('acris-customer-group-available-product.fieldLabelAvailableProducts')"
                    :criteria="searchCriteria"
                    :context="searchContext"
                    :placeholder="$tc('acris-customer-group-available-product.fieldPlaceholderAvailableProducts')"
                    :helpText="$tc('acris-customer-group-available-product.fieldHelpTextAvailableProducts')"
                    v-model="customerGroup.extensions.product">
                </acris-entity-many-to-many-select>
            {% endblock %}
        </sw-card>
    {% endblock %}
{% endblock %}

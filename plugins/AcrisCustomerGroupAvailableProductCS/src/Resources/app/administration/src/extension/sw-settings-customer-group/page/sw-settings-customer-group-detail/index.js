import template from './acris-customer-group-detail.html.twig';

const { mapGetters, mapState } = Shopware.Component.getComponentHelper();
const { Component, Context, Mixin } = Shopware;
const { Criteria } = Shopware.Data;

Component.override('sw-settings-customer-group-detail', {
    template,

    inject: ['repositoryFactory'],

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('placeholder'),
        Mixin.getByName('discard-detail-page-changes')('customerGroup')
    ],

    data() {
        return {
            isLoadingData: false
        };
    },

    computed: {
        ...mapState('swProductDetail', [
            'product'
        ]),

        ...mapGetters('swProductDetail', [
            'isLoading'
        ]),

        isLoadingGrid() {
            return this.isLoadingData || this.isLoading;
        },

        searchCriteria() {
            const criteria = new Criteria();
            criteria.addAssociation('product');
            criteria.addFilter(Criteria.equals('product.parentId', null));

            return criteria;
        },

        searchContext() {
            return {
                ...Context.api,
                inheritance: false
            };
        },

        assignmentRepository() {
            if (!this.customerGroup) return;
            return this.repositoryFactory.create(
                this.customerGroup.extensions.product.entity,
                this.customerGroup.extensions.product.source
            );
        },

        total() {
            if (!this.customerGroup) return;
            if (!this.customerGroup.extensions.product || !Array.isArray(this.customerGroup.extensions.product)) {
                return 0;
            }

            return this.customerGroup.extensions.product.length;
        },

        assignedProductColumns() {
            return [{
                property: 'product.translated.name',
                label: this.$tc('sw-product.list.columnName'),
                primary: true,
                allowResize: true,
                sortable: false
            }, {
                property: 'product.productNumber',
                label: this.$tc('sw-product.list.columnProductNumber'),
                allowResize: true,
                sortable: false
            }];
        },

        productRepository() {
            return this.repositoryFactory.create('product');
        },
    },

    methods: {
        createdComponent() {
            this.parentCreatedComponent();
        },

        onToggleProduct(productId) {
            if (productId === null) {
                return;
            }
            if (!this.customerGroup) return;

            this.isLoadingData = true;
            const matchedAssignedProduct = this.customerGroup.extensions.product.find((assignedProduct) => {
                return assignedProduct.productId === productId;
            });

            if (matchedAssignedProduct) {
                this.removeItem(matchedAssignedProduct);
                this.isLoadingData = false;
            } else {
                const newProduct = this.assignmentRepository.create();
                newProduct.crossSellingId = this.crossSellingId;
                newProduct.productId = productId;
                newProduct.position = this.customerGroup.extensions.product.length + 1;
                this.customerGroup.extensions.product.add(newProduct);

                this.productRepository.get(productId, { ...Context.api, inheritance: true }).then((product) => {
                    newProduct.product = product;
                    this.isLoadingData = false;
                });
            }
        },


        parentCreatedComponent() {
            this.isLoading = true;
            if (this.customerGroupId) {

                this.loadSeoUrls();
                const criteria = new Criteria();
                criteria.addAssociation('registrationSalesChannels');
                criteria.addAssociation('product');
                criteria.addFilter(Criteria.equals('product.parentId', null));

                this.customerGroupRepository.get(this.customerGroupId, Shopware.Context.api, criteria)
                    .then((customerGroup) => {
                        this.customerGroup = customerGroup;
                        this.isLoading = false;
                    });
                return;
            }

            Shopware.State.commit('context/resetLanguageToDefault');
            this.customerGroup = this.customerGroupRepository.create(Shopware.Context.api);
            this.isLoading = false;
        },

        isSelected(item) {
            if (!this.customerGroup) return;
            return this.customerGroup.extensions.product.some(p => p.productId === item.id);
        }
    }
});

<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- Extensions -->
        <service id="Acris\CustomerGroupAvailableProduct\Core\Content\Product\ProductExtension">
            <tag name="shopware.entity.extension"/>
        </service>
        <service id="Acris\CustomerGroupAvailableProduct\Core\Checkout\Customer\Aggregate\CustomerGroup\CustomerGroupExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <!-- Definitions -->
        <service id="Acris\CustomerGroupAvailableProduct\Custom\CustomerGroupAvailableProductDefinition">
            <tag name="shopware.entity.definition" entity="acris_customer_group_available_product" />
        </service>

        <!-- Subscriber -->
        <service id="Acris\CustomerGroupAvailableProduct\Storefront\Subscriber\ProductSubscriber">
            <tag name="kernel.event_subscriber"/>
            <argument type="service" id="Acris\CustomerGroupAvailableProduct\Components\DisplayAllowedProductService"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>

        <service id="Acris\CustomerGroupAvailableProduct\Storefront\Subscriber\ProductListingFeaturesSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <!-- Services -->
        <service id="Acris\CustomerGroupAvailableProduct\Components\DisplayAllowedProductService">
            <argument type="service" id="acris_customer_group_available_product.repository"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="customer.repository"/>
        </service>

        <!-- Service for sitemap exclude -->
        <service id="Acris\CustomerGroupAvailableProduct\Core\Content\Sitemap\Provider\ProductUrlProvider"
                 decorates="Shopware\Core\Content\Sitemap\Provider\ProductUrlProvider">

            <argument type="service" id="product.repository"/>
            <argument type="service" id="Acris\CustomerGroupAvailableProduct\Core\Content\Sitemap\Provider\ProductUrlProvider.inner"/>

            <tag name="shopware.sitemap_url_provider"/>
        </service>

        <!-- SalesChannelProductRepository extension -->
        <service id="Acris\CustomerGroupAvailableProduct\Core\Content\Product\SalesChannel\SalesChannelProductRepository"
                 decorates="sales_channel.product.repository">
            <argument type="service" id="Acris\CustomerGroupAvailableProduct\Core\Content\Product\SalesChannel\SalesChannelProductRepository.inner"/>
            <argument type="service" id="Acris\CustomerGroupAvailableProduct\Components\DisplayAllowedProductService"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>
    </services>
</container>

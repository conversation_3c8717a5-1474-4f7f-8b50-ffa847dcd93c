<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Basic Configuration</title>
        <title lang="de-DE">Grundeinstellungen</title>

        <input-field type="single-select">
            <name>releaseProductsIfNoCustomerGroupAssigned</name>
            <label>Behaviour when no client group has been selected for release</label>
            <label lang="de-DE">Verhalten wenn keine Kundengruppe zur Freigabe ausgewählt wurde</label>
            <helpText>If there are no customer groups assigned for the product, then the product will be released in Storefront.</helpText>
            <helpText lang="de-DE">Wenn für das Produkt keine Kundengruppen zugewiesen sind, wird das Produkt in Storefront freigegeben.</helpText>
            <defaultValue>default</defaultValue>
            <options>
                <option>
                    <id>default</id>
                    <name>ACRIS Standard (release of products only if customer group is entered in designated field)</name>
                    <name lang="de-DE">ACRIS Standard (Freigabe von Produkten nur, wenn Kundengruppe im dafür vorgesehenen Feld eingetragen ist)</name>
                </option>
                <option>
                    <id>unlockIfEmpty</id>
                    <name>Release without customer group assignment (If no customer group is entered in the intended field, then automatically release product for ALL customer groups)</name>
                    <name lang="de-DE">Freigabe ohne Kundengruppenzuordnung (Wenn im vorgesehenen Feld keine Kundengruppe eingetragen ist, dann wird das Produkt automatisch für ALLE Kundengruppen freigegeben)</name>
                </option>
            </options>
        </input-field>
    </card>
</config>

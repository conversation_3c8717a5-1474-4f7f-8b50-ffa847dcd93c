!function(e){var t={};function r(o){if(t[o])return t[o].exports;var n=t[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=e,r.c=t,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(o,n,function(t){return e[t]}.bind(null,n));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p=(window.__sw__.assetPath + '/bundles/acriscustomergroupavailableproductcs/'),r(r.s="0lUs")}({"0lUs":function(e,t,r){"use strict";r.r(t);r("Xo23");function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==o(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===o(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var c=Shopware.Component.getComponentHelper(),s=c.mapGetters,u=c.mapState,l=Shopware,p=l.Component,d=l.Context,m=l.Mixin,f=Shopware.Data.Criteria;p.override("sw-settings-customer-group-detail",{template:'{% block sw_settings_customer_group_detail_content_card %}\n    {% parent %}\n\n    {% block acris_customer_group_available_product_detail_content_card %}\n        <sw-card :isLoading="isLoading" v-if="customerGroup && customerGroup.extensions"\n                 :title="$tc(\'acris-customer-group-available-product.availableProductsCard\')">\n\n            {% block acris_customer_group_available_product_many_to_many_select %}\n                <acris-entity-many-to-many-select\n                    :localMode="true"\n                    :label="$tc(\'acris-customer-group-available-product.fieldLabelAvailableProducts\')"\n                    :criteria="searchCriteria"\n                    :context="searchContext"\n                    :placeholder="$tc(\'acris-customer-group-available-product.fieldPlaceholderAvailableProducts\')"\n                    :helpText="$tc(\'acris-customer-group-available-product.fieldHelpTextAvailableProducts\')"\n                    v-model="customerGroup.extensions.product">\n                </acris-entity-many-to-many-select>\n            {% endblock %}\n        </sw-card>\n    {% endblock %}\n{% endblock %}\n',inject:["repositoryFactory"],mixins:[m.getByName("notification"),m.getByName("placeholder"),m.getByName("discard-detail-page-changes")("customerGroup")],data:function(){return{isLoadingData:!1}},computed:i(i(i({},u("swProductDetail",["product"])),s("swProductDetail",["isLoading"])),{},{isLoadingGrid:function(){return this.isLoadingData||this.isLoading},searchCriteria:function(){var e=new f;return e.addAssociation("product"),e.addFilter(f.equals("product.parentId",null)),e},searchContext:function(){return i(i({},d.api),{},{inheritance:!1})},assignmentRepository:function(){if(this.customerGroup)return this.repositoryFactory.create(this.customerGroup.extensions.product.entity,this.customerGroup.extensions.product.source)},total:function(){if(this.customerGroup)return this.customerGroup.extensions.product&&Array.isArray(this.customerGroup.extensions.product)?this.customerGroup.extensions.product.length:0},assignedProductColumns:function(){return[{property:"product.translated.name",label:this.$tc("sw-product.list.columnName"),primary:!0,allowResize:!0,sortable:!1},{property:"product.productNumber",label:this.$tc("sw-product.list.columnProductNumber"),allowResize:!0,sortable:!1}]},productRepository:function(){return this.repositoryFactory.create("product")}}),methods:{createdComponent:function(){this.parentCreatedComponent()},onToggleProduct:function(e){var t=this;if(null!==e&&this.customerGroup){this.isLoadingData=!0;var r=this.customerGroup.extensions.product.find((function(t){return t.productId===e}));if(r)this.removeItem(r),this.isLoadingData=!1;else{var o=this.assignmentRepository.create();o.crossSellingId=this.crossSellingId,o.productId=e,o.position=this.customerGroup.extensions.product.length+1,this.customerGroup.extensions.product.add(o),this.productRepository.get(e,i(i({},d.api),{},{inheritance:!0})).then((function(e){o.product=e,t.isLoadingData=!1}))}}},parentCreatedComponent:function(){var e=this;if(this.isLoading=!0,this.customerGroupId){this.loadSeoUrls();var t=new f;return t.addAssociation("registrationSalesChannels"),t.addAssociation("product"),t.addFilter(f.equals("product.parentId",null)),void this.customerGroupRepository.get(this.customerGroupId,Shopware.Context.api,t).then((function(t){e.customerGroup=t,e.isLoading=!1}))}Shopware.State.commit("context/resetLanguageToDefault"),this.customerGroup=this.customerGroupRepository.create(Shopware.Context.api),this.isLoading=!1},isSelected:function(e){if(this.customerGroup)return this.customerGroup.extensions.product.some((function(t){return t.productId===e.id}))}}});r("AkOs");var h=Shopware.Mixin;Shopware.Component.override("sw-product-detail-base",{template:'{% block sw_product_detail_base_basic_info_card %}\n    {% parent %}\n\n    {% block acris_customer_group_available_product_customer_group_card %}\n        <sw-card :isLoading="isLoading" v-if="product && product.extensions && !hasParent"\n                 :title="$tc(\'acris-product-customer-group.availableProductsCard\')"\n                 class="acris-product-customer-group">\n            {% block acris_customer_group_available_product_many_to_many_select %}\n\n{#                <acris-entity-many-to-many-select#}\n{#                    :localMode="true"#}\n{#                    v-model="product.extensions.acrisReleaseCustomerGroup"#}\n{#                    :label="$tc(\'acris-product-customer-group.fieldLabelAvailableProducts\')"#}\n{#                    :placeholder="$tc(\'acris-product-customer-group.fieldPlaceholderAvailableProducts\')"#}\n{#                    :helpText="$tc(\'acris-product-customer-group.fieldHelpTextAvailableProducts\')">#}\n{#                </acris-entity-many-to-many-select>#}\n\n                <sw-inherit-wrapper v-model="product.extensions.acrisReleaseCustomerGroup"\n                                    :hasParent="hasParent"\n                                    isAssociation>\n                    <template #content="{ currentValue, isInherited, updateCurrentValue }">\n                        <acris-entity-many-to-many-select\n                            :localMode="true"\n                            :entityCollection="currentValue"\n                            :disabled="isInherited"\n                            :key="isInherited"\n                            :label="$tc(\'acris-product-customer-group.fieldLabelAvailableProducts\')"\n                            :placeholder="$tc(\'acris-product-customer-group.fieldPlaceholderAvailableProducts\')"\n                            @change="updateCurrentValue"\n                            :helpText="$tc(\'acris-product-customer-group.fieldHelpTextAvailableProducts\')">\n                        </acris-entity-many-to-many-select>\n                    </template>\n                </sw-inherit-wrapper>\n            {% endblock %}\n\n            {% block acris_customer_group_available_product_exclude_sitemap_exclude %}\n                <sw-checkbox-field :label="$tc(\'acris-product-customer-group.fieldLabelExcludeSitemap\')"\n                                   @change="onChangeExcludeSitemapCheckbox"\n                                   v-model="acris_customer_group_available_product_exclude_sitemap">\n                </sw-checkbox-field>\n            {% endblock %}\n        </sw-card>\n    {% endblock %}\n{% endblock %}\n',data:function(){return{acris_customer_group_available_product_exclude_sitemap:!1}},mixins:[h.getByName("notification"),h.getByName("placeholder")],updated:function(){this.refreshSitemapCheckboxValue()},computed:{hasParent:function(){return!!this.parentProduct.id}},methods:{createdComponent:function(){this.$super("createdComponent"),this.refreshSitemapCheckboxValue()},refreshSitemapCheckboxValue:function(){null!=this.product.customFields?this.acris_customer_group_available_product_exclude_sitemap=this.product.customFields.acris_customer_group_available_product_exclude_sitemap:this.acris_customer_group_available_product_exclude_sitemap=!1},onChangeExcludeSitemapCheckbox:function(){this.createDefaultCustomFieldsIfNotExists(),this.product.customFields={acris_customer_group_available_product_exclude_sitemap:this.acris_customer_group_available_product_exclude_sitemap}},createDefaultCustomFieldsIfNotExists:function(){null==this.product.customFields&&(this.product.customFields={acris_customer_group_available_product_exclude_sitemap:!1})}}})},AkOs:function(e,t){Shopware.Component.override("sw-product-detail",{computed:{productCriteria:function(){var e=this.$super("productCriteria");return e.addAssociation("acrisReleaseCustomerGroup"),e}}})},Xo23:function(e,t){Shopware.Component.extend("acris-entity-many-to-many-select","sw-entity-many-to-many-select",{updated:function(){this.updatedComponent()},methods:{updatedComponent:function(){this.initData()}}})}});
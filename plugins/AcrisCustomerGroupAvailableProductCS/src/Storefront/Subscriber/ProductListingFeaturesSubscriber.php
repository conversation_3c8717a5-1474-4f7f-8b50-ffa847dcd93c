<?php declare(strict_types=1);

namespace Acris\CustomerGroupAvailableProduct\Storefront\Subscriber;

use Shopware\Core\Content\Category\Event\CategoryRouteCacheKeyEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProductListingFeaturesSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents()
    {
        return [
            CategoryRouteCacheKeyEvent::class => [
                ['onCategoryRouteCacheKey', 200]
            ]
        ];
    }

    public function onCategoryRouteCacheKey(CategoryRouteCacheKeyEvent $event): void
    {
        if (empty($event->getContext()) || empty($event->getContext()->getCustomer()) || empty($event->getContext()->getCurrentCustomerGroup())) return;

        $event->addPart($event->getContext()->getCurrentCustomerGroup()->getId());
    }
}

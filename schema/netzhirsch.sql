CREATE TABLE `netzhirsch_order_countdown_information` (
    `id` BINARY(16) NOT NULL,
    `hide_order_countdown` TINYINT(1) NOT NULL DEFAULT '0',
    `minutes_to_switch_display_mode` INT(11) NULL,
    `product_id` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `netzhirsch_order_countdown_order` (
    `id` BINARY(16) NOT NULL,
    `date` DATETIME(3) NOT NULL,
    `deadline` DATETIME(3) NOT NULL,
    `delivery_date` DATETIME(3) NOT NULL,
    `order_id` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `netzhirsch_order_countdown_holiday` (
    `id` BINARY(16) NOT NULL,
    `date` DATE NOT NULL,
    `deadline` DATETIME(3) NULL,
    `name` VARCHAR(255) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
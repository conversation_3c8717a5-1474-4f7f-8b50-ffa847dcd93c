CREATE TABLE `acris_product_display_image` (
    `id` BINARY(16) NOT NULL,
    `product_media_id` BINARY(16) NULL,
    `language_ids` JSON NULL,
    `use_image_as_cover_for_language` <PERSON><PERSON><PERSON> NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.acris_product_display_image.language_ids` CHECK (JSON_VALID(`language_ids`)),
    CONSTRAINT `json.acris_product_display_image.use_image_as_cover_for_language` CHECK (JSON_VALID(`use_image_as_cover_for_language`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_product_display_image_language` (
    `display_image_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`display_image_id`,`language_id`),
    KEY `fk.acris_product_display_image_language.display_image_id` (`display_image_id`),
    KEY `fk.acris_product_display_image_language.language_id` (`language_id`),
    CONSTRAINT `fk.acris_product_display_image_language.display_image_id` FOREIGN KEY (`display_image_id`) REFERENCES `acris_product_display_image` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.acris_product_display_image_language.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_product_video` (
    `id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `media_id` BINARY(16) NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `priority` INT(11) NULL,
    `type` VARCHAR(255) NULL,
    `meta_data` JSON NULL,
    `meta_data_created_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.acris_product_video.meta_data` CHECK (JSON_VALID(`meta_data`)),
    CONSTRAINT `json.acris_product_video.translated` CHECK (JSON_VALID(`translated`)),
    KEY `fk.acris_product_video.product_id` (`product_id`,`product_version_id`),
    KEY `fk.acris_product_video.media_id` (`media_id`),
    CONSTRAINT `fk.acris_product_video.product_id` FOREIGN KEY (`product_id`,`product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.acris_product_video.media_id` FOREIGN KEY (`media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_product_video_translation` (
    `link` VARCHAR(255) NULL,
    `description` LONGTEXT NULL,
    `embed_code` LONGTEXT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `acris_product_video_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`acris_product_video_id`,`language_id`),
    KEY `fk.acris_product_video_translation.acris_product_video_id` (`acris_product_video_id`),
    KEY `fk.acris_product_video_translation.language_id` (`language_id`),
    CONSTRAINT `fk.acris_product_video_translation.acris_product_video_id` FOREIGN KEY (`acris_product_video_id`) REFERENCES `acris_product_video` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.acris_product_video_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_stock_notification` (
    `id` BINARY(16) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `status` VARCHAR(255) NOT NULL,
    `hash` VARCHAR(255) NOT NULL,
    `custom_fields` JSON NULL,
    `confirmed_at` DATETIME(3) NULL,
    `notified` TINYINT(1) NULL DEFAULT '0',
    `registered` TINYINT(1) NULL DEFAULT '0',
    `notified_date_time` DATETIME(3) NULL,
    `language_id` BINARY(16) NOT NULL,
    `sales_channel_id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.acris_stock_notification.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.acris_stock_notification.language_id` (`language_id`),
    KEY `fk.acris_stock_notification.sales_channel_id` (`sales_channel_id`),
    KEY `fk.acris_stock_notification.product_id` (`product_id`,`product_version_id`),
    CONSTRAINT `fk.acris_stock_notification.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.acris_stock_notification.sales_channel_id` FOREIGN KEY (`sales_channel_id`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.acris_stock_notification.product_id` FOREIGN KEY (`product_id`,`product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_stock_notification_product` (
    `id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `registered` INT(11) NULL,
    `sent` INT(11) NULL,
    `open` INT(11) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.acris_stock_notification_product.product_id` (`product_id`,`product_version_id`),
    CONSTRAINT `fk.acris_stock_notification_product.product_id` FOREIGN KEY (`product_id`,`product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `acris_promotion_display` (
    `id` BINARY(16) NOT NULL,
    `promotion_id` BINARY(16) NOT NULL,
    `discount_display` VARCHAR(255) NULL,
    `discount_info` TINYINT(1) NULL DEFAULT '0',
    `display_position` VARCHAR(255) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
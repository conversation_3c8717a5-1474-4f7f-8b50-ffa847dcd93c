CREATE TABLE `zeobv_product_bundle_connection` (
    `id` BINARY(16) NOT NULL,
    `bundle_product_id` BINARY(16) NOT NULL,
    `bundle_product_version_id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `quantity` INT(11) NULL,
    `position` INT(11) NULL,
    `comment` VARCHAR(255) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.zeobv_product_bundle_connection.bundle_product_id` (`bundle_product_id`,`bundle_product_version_id`),
    KEY `fk.zeobv_product_bundle_connection.product_id` (`product_id`,`bundle_product_version_id`),
    CONSTRAINT `fk.zeobv_product_bundle_connection.bundle_product_id` FOREIGN KEY (`bundle_product_id`,`bundle_product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.zeobv_product_bundle_connection.product_id` FOREIGN KEY (`product_id`,`bundle_product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE `templaid_currency_symbol` (
    `id` BINARY(16) NOT NULL,
    `currency_id` BINARY(16) NULL,
    `use_currency_symbol` TINYINT(1) NULL DEFAULT '0',
    `place_symbol_first` TINYINT(1) NULL DEFAULT '0',
    `remove_whitespace` TINYINT(1) NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
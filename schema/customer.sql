CREATE TABLE `customer` (
    `id` BINARY(16) NOT NULL,
    `customer_group_id` BINARY(16) NOT NULL,
    `default_payment_method_id` BINARY(16) NOT NULL,
    `sales_channel_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    `last_payment_method_id` BINARY(16) NULL,
    `default_billing_address_id` BINARY(16) NOT NULL,
    `default_shipping_address_id` BINARY(16) NOT NULL,
    `auto_increment` INT(11) NULL,
    `customer_number` VARCHAR(255) NOT NULL,
    `salutation_id` BINARY(16) NULL,
    `first_name` VA<PERSON>HA<PERSON>(255) NOT NULL,
    `last_name` VA<PERSON>HA<PERSON>(255) NOT NULL,
    `company` VARCHAR(255) NULL,
    `password` VARCHAR(1024) NULL,
    `email` VARCHAR(255) NOT NULL,
    `title` VARCHAR(255) NULL,
    `vat_ids` <PERSON><PERSON>N NULL,
    `affiliate_code` VARCHAR(255) NULL,
    `campaign_code` VARCHAR(255) NULL,
    `active` TINYINT(1) NULL DEFAULT '0',
    `double_opt_in_registration` TINYINT(1) NULL DEFAULT '0',
    `double_opt_in_email_sent_date` DATETIME(3) NULL,
    `double_opt_in_confirm_date` DATETIME(3) NULL,
    `hash` VARCHAR(255) NULL,
    `guest` TINYINT(1) NULL DEFAULT '0',
    `first_login` DATETIME(3) NULL,
    `last_login` DATETIME(3) NULL,
    `newsletter_sales_channel_ids` JSON NULL,
    `birthday` DATE NULL,
    `last_order_date` DATETIME(3) NULL,
    `order_count` INT(11) NULL,
    `order_total_amount` DOUBLE NULL,
    `review_count` INT(11) NULL,
    `custom_fields` JSON NULL,
    `legacy_password` VARCHAR(255) NULL,
    `legacy_encoder` VARCHAR(255) NULL,
    `remote_address` VARCHAR(255) NULL,
    `tag_ids` JSON NULL,
    `requested_customer_group_id` BINARY(16) NULL,
    `bound_sales_channel_id` BINARY(16) NULL,
    `account_type` VARCHAR(255) NOT NULL,
    `created_by_id` BINARY(16) NULL,
    `updated_by_id` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.customer.vat_ids` CHECK (JSON_VALID(`vat_ids`)),
    CONSTRAINT `json.customer.newsletter_sales_channel_ids` CHECK (JSON_VALID(`newsletter_sales_channel_ids`)),
    CONSTRAINT `json.customer.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    CONSTRAINT `json.customer.tag_ids` CHECK (JSON_VALID(`tag_ids`)),
    KEY `fk.customer.customer_group_id` (`customer_group_id`),
    KEY `fk.customer.default_payment_method_id` (`default_payment_method_id`),
    KEY `fk.customer.sales_channel_id` (`sales_channel_id`),
    KEY `fk.customer.language_id` (`language_id`),
    KEY `fk.customer.last_payment_method_id` (`last_payment_method_id`),
    KEY `fk.customer.default_billing_address_id` (`default_billing_address_id`),
    KEY `fk.customer.active_billing_address_id` (`active_billing_address_id`),
    KEY `fk.customer.default_shipping_address_id` (`default_shipping_address_id`),
    KEY `fk.customer.active_shipping_address_id` (`active_shipping_address_id`),
    KEY `fk.customer.salutation_id` (`salutation_id`),
    KEY `fk.customer.requested_customer_group_id` (`requested_customer_group_id`),
    KEY `fk.customer.bound_sales_channel_id` (`bound_sales_channel_id`),
    KEY `fk.customer.created_by_id` (`created_by_id`),
    KEY `fk.customer.updated_by_id` (`updated_by_id`),
    CONSTRAINT `fk.customer.customer_group_id` FOREIGN KEY (`customer_group_id`) REFERENCES `customer_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.default_payment_method_id` FOREIGN KEY (`default_payment_method_id`) REFERENCES `payment_method` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.sales_channel_id` FOREIGN KEY (`sales_channel_id`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.last_payment_method_id` FOREIGN KEY (`last_payment_method_id`) REFERENCES `payment_method` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.salutation_id` FOREIGN KEY (`salutation_id`) REFERENCES `salutation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.requested_customer_group_id` FOREIGN KEY (`requested_customer_group_id`) REFERENCES `customer_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.bound_sales_channel_id` FOREIGN KEY (`bound_sales_channel_id`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.created_by_id` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer.updated_by_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_group_translation` (
    `name` VARCHAR(255) NOT NULL,
    `registration_title` VARCHAR(255) NULL,
    `registration_introduction` LONGTEXT NULL,
    `registration_only_company_registration` TINYINT(1) NULL DEFAULT '0',
    `registration_seo_meta_description` LONGTEXT NULL,
    `custom_fields` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `customer_group_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`customer_group_id`,`language_id`),
    CONSTRAINT `json.customer_group_translation.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.customer_group_translation.customer_group_id` (`customer_group_id`),
    KEY `fk.customer_group_translation.language_id` (`language_id`),
    CONSTRAINT `fk.customer_group_translation.customer_group_id` FOREIGN KEY (`customer_group_id`) REFERENCES `customer_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_group_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_address` (
    `id` BINARY(16) NOT NULL,
    `customer_id` BINARY(16) NOT NULL,
    `country_id` BINARY(16) NOT NULL,
    `country_state_id` BINARY(16) NULL,
    `salutation_id` BINARY(16) NULL,
    `first_name` VARCHAR(255) NOT NULL,
    `last_name` VARCHAR(255) NOT NULL,
    `zipcode` VARCHAR(255) NULL,
    `city` VARCHAR(255) NOT NULL,
    `company` VARCHAR(255) NULL,
    `street` VARCHAR(255) NOT NULL,
    `department` VARCHAR(255) NULL,
    `title` VARCHAR(255) NULL,
    `phone_number` VARCHAR(255) NULL,
    `additional_address_line1` VARCHAR(255) NULL,
    `additional_address_line2` VARCHAR(255) NULL,
    `custom_fields` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.customer_address.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.customer_address.customer_id` (`customer_id`),
    KEY `fk.customer_address.country_id` (`country_id`),
    KEY `fk.customer_address.country_state_id` (`country_state_id`),
    KEY `fk.customer_address.salutation_id` (`salutation_id`),
    CONSTRAINT `fk.customer_address.customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_address.country_id` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_address.country_state_id` FOREIGN KEY (`country_state_id`) REFERENCES `country_state` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_address.salutation_id` FOREIGN KEY (`salutation_id`) REFERENCES `salutation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_recovery` (
    `id` BINARY(16) NOT NULL,
    `hash` VARCHAR(255) NOT NULL,
    `customer_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_group` (
    `id` BINARY(16) NOT NULL,
    `display_gross` TINYINT(1) NULL DEFAULT '0',
    `registration_active` TINYINT(1) NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.customer_group.translated` CHECK (JSON_VALID(`translated`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_group_registration_sales_channels` (
    `customer_group_id` BINARY(16) NOT NULL,
    `sales_channel_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`customer_group_id`,`sales_channel_id`),
    KEY `fk.customer_group_registration_sales_channels.customer_group_id` (`customer_group_id`),
    KEY `fk.customer_group_registration_sales_channels.sales_channel_id` (`sales_channel_id`),
    CONSTRAINT `fk.customer_group_registration_sales_channels.customer_group_id` FOREIGN KEY (`customer_group_id`) REFERENCES `customer_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_group_registration_sales_channels.sales_channel_id` FOREIGN KEY (`sales_channel_id`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_tag` (
    `customer_id` BINARY(16) NOT NULL,
    `tag_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`customer_id`,`tag_id`),
    KEY `fk.customer_tag.customer_id` (`customer_id`),
    KEY `fk.customer_tag.tag_id` (`tag_id`),
    CONSTRAINT `fk.customer_tag.customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_tag.tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_wishlist` (
    `id` BINARY(16) NOT NULL,
    `customer_id` BINARY(16) NOT NULL,
    `sales_channel_id` BINARY(16) NOT NULL,
    `custom_fields` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.customer_wishlist.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.customer_wishlist.customer_id` (`customer_id`),
    KEY `fk.customer_wishlist.sales_channel_id` (`sales_channel_id`),
    CONSTRAINT `fk.customer_wishlist.customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_wishlist.sales_channel_id` FOREIGN KEY (`sales_channel_id`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `customer_wishlist_product` (
    `id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `customer_wishlist_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.customer_wishlist_product.customer_wishlist_id` (`customer_wishlist_id`),
    KEY `fk.customer_wishlist_product.product_id` (`product_id`,`product_version_id`),
    CONSTRAINT `fk.customer_wishlist_product.customer_wishlist_id` FOREIGN KEY (`customer_wishlist_id`) REFERENCES `customer_wishlist` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.customer_wishlist_product.product_id` FOREIGN KEY (`product_id`,`product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
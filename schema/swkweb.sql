CREATE TABLE `swkweb_product_set` (
    `id` BINARY(16) NOT NULL,
    `price_calculation` VARCHAR(255) NULL,
    `include_set_product_price` TINYINT(1) NOT NULL DEFAULT '0',
    `include_set_product_delivery_time` TINYINT(1) NOT NULL DEFAULT '0',
    `show_main_product_as_main_item` TINYINT(1) NOT NULL DEFAULT '0',
    `show_configurator_template_slider` TINYINT(1) NOT NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.swkweb_product_set.translated` CHECK (JSON_VALID(`translated`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_translation` (
    `name` VARCHAR(255) NOT NULL,
    `custom_fields` J<PERSON><PERSON> NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `swkweb_product_set_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`swkweb_product_set_id`,`language_id`),
    CONSTRAINT `json.swkweb_product_set_translation.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.swkweb_product_set_translation.swkweb_product_set_id` (`swkweb_product_set_id`),
    KEY `fk.swkweb_product_set_translation.language_id` (`language_id`),
    CONSTRAINT `fk.swkweb_product_set_translation.swkweb_product_set_id` FOREIGN KEY (`swkweb_product_set_id`) REFERENCES `swkweb_product_set` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_product` (
    `id` BINARY(16) NOT NULL,
    `swkweb_product_set_id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.swkweb_product_set_product.swkweb_product_set_id` (`swkweb_product_set_id`),
    CONSTRAINT `fk.swkweb_product_set_product.swkweb_product_set_id` FOREIGN KEY (`swkweb_product_set_id`) REFERENCES `swkweb_product_set` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_slot` (
    `id` BINARY(16) NOT NULL,
    `swkweb_product_set_price_calculation_id` BINARY(16) NULL,
    `fallback_media_id` BINARY(16) NULL,
    `template` VARCHAR(255) NULL,
    `display_settings` JSON NULL,
    `minimum_selected_options` INT(11) NOT NULL,
    `maximum_selected_options` INT(11) NOT NULL,
    `limit_aggregated_option_quantities` TINYINT(1) NOT NULL DEFAULT '0',
    `preset` TINYINT(1) NOT NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.swkweb_product_set_slot.display_settings` CHECK (JSON_VALID(`display_settings`)),
    CONSTRAINT `json.swkweb_product_set_slot.translated` CHECK (JSON_VALID(`translated`)),
    KEY `fk.swkweb_product_set_slot.swkweb_product_set_price_calculation_id` (`swkweb_product_set_price_calculation_id`),
    KEY `fk.swkweb_product_set_slot.fallback_media_id` (`fallback_media_id`),
    CONSTRAINT `fk.swkweb_product_set_slot.swkweb_product_set_price_calculation_id` FOREIGN KEY (`swkweb_product_set_price_calculation_id`) REFERENCES `swkweb_product_set_price_calculation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_slot.fallback_media_id` FOREIGN KEY (`fallback_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_slot_translation` (
    `name` VARCHAR(255) NOT NULL,
    `description` VARCHAR(255) NULL,
    `custom_fields` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `swkweb_product_set_slot_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`swkweb_product_set_slot_id`,`language_id`),
    CONSTRAINT `json.swkweb_product_set_slot_translation.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.swkweb_product_set_slot_translation.swkweb_product_set_slot_id` (`swkweb_product_set_slot_id`),
    KEY `fk.swkweb_product_set_slot_translation.language_id` (`language_id`),
    CONSTRAINT `fk.swkweb_product_set_slot_translation.swkweb_product_set_slot_id` FOREIGN KEY (`swkweb_product_set_slot_id`) REFERENCES `swkweb_product_set_slot` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_slot_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_slot_assignment` (
    `id` BINARY(16) NOT NULL,
    `swkweb_product_set_id` BINARY(16) NOT NULL,
    `swkweb_product_set_slot_id` BINARY(16) NOT NULL,
    `position` INT(11) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.swkweb_product_set_slot_assignment.swkweb_product_set_id` (`swkweb_product_set_id`),
    KEY `fk.swkweb_product_set_slot_assignment.swkweb_product_set_slot_id` (`swkweb_product_set_slot_id`),
    CONSTRAINT `fk.swkweb_product_set_slot_assignment.swkweb_product_set_id` FOREIGN KEY (`swkweb_product_set_id`) REFERENCES `swkweb_product_set` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_slot_assignment.swkweb_product_set_slot_id` FOREIGN KEY (`swkweb_product_set_slot_id`) REFERENCES `swkweb_product_set_slot` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_option` (
    `id` BINARY(16) NOT NULL,
    `swkweb_product_set_slot_id` BINARY(16) NOT NULL,
    `option_product_id` BINARY(16) NOT NULL,
    `option_product_version_id` BINARY(16) NOT NULL,
    `swkweb_product_set_price_calculation_id` BINARY(16) NULL,
    `minimum_quantity` INT(11) NOT NULL,
    `maximum_quantity` INT(11) NOT NULL,
    `quantity_step` INT(11) NOT NULL,
    `position` INT(11) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.swkweb_product_set_option.translated` CHECK (JSON_VALID(`translated`)),
    KEY `fk.swkweb_product_set_option.swkweb_product_set_slot_id` (`swkweb_product_set_slot_id`),
    KEY `fk.swkweb_product_set_option.option_product_id` (`option_product_id`,`option_product_version_id`),
    KEY `fk.swkweb_product_set_option.swkweb_product_set_price_calculation_id` (`swkweb_product_set_price_calculation_id`),
    CONSTRAINT `fk.swkweb_product_set_option.swkweb_product_set_slot_id` FOREIGN KEY (`swkweb_product_set_slot_id`) REFERENCES `swkweb_product_set_slot` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_option.option_product_id` FOREIGN KEY (`option_product_id`,`option_product_version_id`) REFERENCES `product` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_option.swkweb_product_set_price_calculation_id` FOREIGN KEY (`swkweb_product_set_price_calculation_id`) REFERENCES `swkweb_product_set_price_calculation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_option_translation` (
    `name` VARCHAR(255) NULL,
    `modal_content` LONGTEXT NULL,
    `custom_fields` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `swkweb_product_set_option_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`swkweb_product_set_option_id`,`language_id`),
    CONSTRAINT `json.swkweb_product_set_option_translation.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
    KEY `fk.swkweb_product_set_option_translation.swkweb_product_set_option_id` (`swkweb_product_set_option_id`),
    KEY `fk.swkweb_product_set_option_translation.language_id` (`language_id`),
    CONSTRAINT `fk.swkweb_product_set_option_translation.swkweb_product_set_option_id` FOREIGN KEY (`swkweb_product_set_option_id`) REFERENCES `swkweb_product_set_option` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_option_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_price_calculation` (
    `id` BINARY(16) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `price_calculation` VARCHAR(255) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `swkweb_product_set_price_calculation_rule` (
    `id` BINARY(16) NOT NULL,
    `rule_id` BINARY(16) NOT NULL,
    `swkweb_product_set_price_calculation_id` BINARY(16) NOT NULL,
    `price_calculation` VARCHAR(255) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.swkweb_product_set_price_calculation_rule.swkweb_product_set_price_calculation_id` (`swkweb_product_set_price_calculation_id`),
    KEY `fk.swkweb_product_set_price_calculation_rule.rule_id` (`rule_id`),
    CONSTRAINT `fk.swkweb_product_set_price_calculation_rule.swkweb_product_set_price_calculation_id` FOREIGN KEY (`swkweb_product_set_price_calculation_id`) REFERENCES `swkweb_product_set_price_calculation` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.swkweb_product_set_price_calculation_rule.rule_id` FOREIGN KEY (`rule_id`) REFERENCES `rule` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
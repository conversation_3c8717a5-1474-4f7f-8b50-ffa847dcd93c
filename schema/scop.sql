CREATE TABLE `scop_platform_redirecter_redirect` (
    `id` BINARY(16) NOT NULL,
    `sourceURL` VARCHAR(255) NULL,
    `targetURL` VARCHAR(255) NULL,
    `httpCode` INT(11) NULL,
    `enabled` TINYINT(1) NULL DEFAULT '0',
    `queryParamsHandling` INT(11) NULL,
    `salesChannelId` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.scop_platform_redirecter_redirect.salesChannelId` (`salesChannelId`),
    CONSTRAINT `fk.scop_platform_redirecter_redirect.salesChannelId` FOREIGN KEY (`salesChannelId`) REFERENCES `sales_channel` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
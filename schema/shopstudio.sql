CREATE TABLE `shopstudio_pixelyourshop_pixel` (
    `id` BINARY(16) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `sales_channel_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    `version` INT(11) NOT NULL,
    `config` JSON NOT NULL,
    `opt_in_active` TINYINT(1) NOT NULL DEFAULT '0',
    `opt_in_code` LONGTEXT NULL,
    `use_custom_opt_in_code` TINYINT(1) NOT NULL DEFAULT '0',
    `custom_opt_in_code` LONGTEXT NULL,
    `opt_in_no_script_code` LONGTEXT NULL,
    `use_custom_opt_in_no_script_code` TINYINT(1) NOT NULL DEFAULT '0',
    `custom_opt_in_no_script_code` LONGTEXT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.shopstudio_pixelyourshop_pixel.config` CHECK (JSON_VALID(`config`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `shopstudio_pixelyourshop_pixel_event` (
    `id` BINARY(16) NOT NULL,
    `pixel_id` BINARY(16) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `version` INT(11) NOT NULL,
    `active` TINYINT(1) NOT NULL DEFAULT '0',
    `code` LONGTEXT NULL,
    `use_custom_code` TINYINT(1) NOT NULL DEFAULT '0',
    `custom_code` LONGTEXT NULL,
    `payload` JSON NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.shopstudio_pixelyourshop_pixel_event.payload` CHECK (JSON_VALID(`payload`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE `flink_size_chart` (
    `id` BINARY(16) NOT NULL,
    `expanded_tabs` TINYINT(1) NULL DEFAULT '0',
    `thead_classes` JSON NULL,
    `table_classes` <PERSON><PERSON><PERSON> NULL,
    `table_head_background` VARCHAR(255) NULL,
    `table_head_color` VARCHAR(255) NULL,
    `expanded_tab_title_background` VARCHAR(255) NULL,
    `expanded_tab_title_color` VARCHAR(255) NULL,
    `property_group_option_id` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.flink_size_chart.thead_classes` CHECK (JSON_VALID(`thead_classes`)),
    CONSTRAINT `json.flink_size_chart.table_classes` CHECK (JSON_VALID(`table_classes`)),
    CONSTRAINT `json.flink_size_chart.translated` CHECK (JSON_VALID(`translated`)),
    KEY `fk.flink_size_chart.id` (`id`),
    KEY `fk.flink_size_chart.property_group_option` (`property_group_option`),
    CONSTRAINT `fk.flink_size_chart.id` FOREIGN KEY (`id`) REFERENCES `flink_size_chart_product` (`flink_size_chart_id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart.property_group_option` FOREIGN KEY (`property_group_option`) REFERENCES `property_group_option` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_translation` (
    `name` VARCHAR(255) NULL,
    `display_name` VARCHAR(255) NULL,
    `charts` JSON NULL,
    `textBefore` LONGTEXT NULL,
    `textAfter` LONGTEXT NULL,
    `media_before` BINARY(16) NULL,
    `media_after` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `flink_size_chart_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`flink_size_chart_id`,`language_id`),
    CONSTRAINT `json.flink_size_chart_translation.charts` CHECK (JSON_VALID(`charts`)),
    KEY `fk.flink_size_chart_translation.media_before` (`media_before`),
    KEY `fk.flink_size_chart_translation.media_after` (`media_after`),
    KEY `fk.flink_size_chart_translation.flink_size_chart_id` (`flink_size_chart_id`),
    KEY `fk.flink_size_chart_translation.language_id` (`language_id`),
    CONSTRAINT `fk.flink_size_chart_translation.media_before` FOREIGN KEY (`media_before`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_translation.media_after` FOREIGN KEY (`media_after`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_translation.flink_size_chart_id` FOREIGN KEY (`flink_size_chart_id`) REFERENCES `flink_size_chart` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_product` (
    `id` BINARY(16) NOT NULL,
    `flink_size_chart_id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `product_version_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.flink_size_chart_product.translated` CHECK (JSON_VALID(`translated`)),
    KEY `fk.flink_size_chart_product.flink_size_chart_id` (`flink_size_chart_id`),
    CONSTRAINT `fk.flink_size_chart_product.flink_size_chart_id` FOREIGN KEY (`flink_size_chart_id`) REFERENCES `flink_size_chart` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_product_translation` (
    `charts_override` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    `flink_size_chart_product_id` BINARY(16) NOT NULL,
    `language_id` BINARY(16) NOT NULL,
    PRIMARY KEY (`flink_size_chart_product_id`,`language_id`),
    CONSTRAINT `json.flink_size_chart_product_translation.charts_override` CHECK (JSON_VALID(`charts_override`)),
    KEY `fk.flink_size_chart_product_translation.flink_size_chart_product_id` (`flink_size_chart_product_id`),
    KEY `fk.flink_size_chart_product_translation.language_id` (`language_id`),
    CONSTRAINT `fk.flink_size_chart_product_translation.flink_size_chart_product_id` FOREIGN KEY (`flink_size_chart_product_id`) REFERENCES `flink_size_chart_product` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_product_translation.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_category` (
    `flink_size_chart_id` BINARY(16) NOT NULL,
    `category_id` BINARY(16) NOT NULL,
    `category_version_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`flink_size_chart_id`,`category_id`,`category_version_id`),
    KEY `fk.flink_size_chart_category.flink_size_chart_id` (`flink_size_chart_id`),
    KEY `fk.flink_size_chart_category.category_id` (`category_id`,`category_version_id`),
    CONSTRAINT `fk.flink_size_chart_category.flink_size_chart_id` FOREIGN KEY (`flink_size_chart_id`) REFERENCES `flink_size_chart` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_category.category_id` FOREIGN KEY (`category_id`,`category_version_id`) REFERENCES `category` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_product_stream` (
    `flink_size_chart_id` BINARY(16) NOT NULL,
    `product_stream_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`flink_size_chart_id`,`product_stream_id`),
    KEY `fk.flink_size_chart_product_stream.flink_size_chart_id` (`flink_size_chart_id`),
    KEY `fk.flink_size_chart_product_stream.product_stream_id` (`product_stream_id`),
    CONSTRAINT `fk.flink_size_chart_product_stream.flink_size_chart_id` FOREIGN KEY (`flink_size_chart_id`) REFERENCES `flink_size_chart` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_product_stream.product_stream_id` FOREIGN KEY (`product_stream_id`) REFERENCES `product_stream` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `flink_size_chart_category_manufacturer` (
    `id` BINARY(16) NOT NULL,
    `flink_size_chart_id` BINARY(16) NOT NULL,
    `category_id` BINARY(16) NOT NULL,
    `category_version_id` BINARY(16) NOT NULL,
    `product_manufacturer_id` BINARY(16) NOT NULL,
    `product_manufacturer_version_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.flink_size_chart_category_manufacturer.flink_size_chart_id` (`flink_size_chart_id`),
    KEY `fk.flink_size_chart_category_manufacturer.category_id` (`category_id`,`category_version_id`),
    KEY `fk.flink_size_chart_category_manufacturer.product_manufacturer_id` (`product_manufacturer_id`,`product_manufacturer_version_id`),
    CONSTRAINT `fk.flink_size_chart_category_manufacturer.flink_size_chart_id` FOREIGN KEY (`flink_size_chart_id`) REFERENCES `flink_size_chart` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_category_manufacturer.category_id` FOREIGN KEY (`category_id`,`category_version_id`) REFERENCES `category` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.flink_size_chart_category_manufacturer.product_manufacturer_id` FOREIGN KEY (`product_manufacturer_id`,`product_manufacturer_version_id`) REFERENCES `product_manufacturer` (`id`,`version_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
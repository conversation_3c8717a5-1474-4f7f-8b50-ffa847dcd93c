CREATE TABLE `kplngi_productcategoryposition` (
    `id` BINARY(16) NOT NULL,
    `product_id` BINARY(16) NOT NULL,
    `category_id` BINARY(16) NOT NULL,
    `product_parent_id` BINARY(16) NULL,
    `position` INT(11) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    KEY `fk.kplngi_productcategoryposition.product_id` (`product_id`),
    KEY `fk.kplngi_productcategoryposition.category_id` (`category_id`),
    KEY `fk.kplngi_productcategoryposition.product_parent_id` (`product_parent_id`),
    CONSTRAINT `fk.kplngi_productcategoryposition.product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.kplngi_productcategoryposition.category_id` FOREI<PERSON>N KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk.kplngi_productcategoryposition.product_parent_id` FOREIGN KEY (`product_parent_id`) REFERENCES `product` (`parent_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `kplngi_orderactive` (
    `id` BINARY(16) NOT NULL,
    `category_id` BINARY(16) NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
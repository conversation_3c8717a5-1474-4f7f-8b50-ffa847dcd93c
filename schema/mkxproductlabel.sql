CREATE TABLE `mkxproductlabel_label` (
    `id` BINARY(16) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `width_in_mm` DOUBLE NOT NULL,
    `height_in_mm` DOUBLE NOT NULL,
    `margin_left_in_mm` DOUBLE NOT NULL,
    `margin_top_in_mm` DOUBLE NOT NULL,
    `margin_right_in_mm` DOUBLE NOT NULL,
    `margin_bottom_in_mm` DOUBLE NOT NULL,
    `template` LONGTEXT NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `mkxproductlabel_page` (
    `id` BINARY(16) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `width_in_mm` DOUBLE NOT NULL,
    `height_in_mm` DOUBLE NOT NULL,
    `margin_left_in_mm` DOUBLE NOT NULL,
    `margin_top_in_mm` DOUBLE NOT NULL,
    `margin_right_in_mm` DOUBLE NOT NULL,
    `margin_bottom_in_mm` DOUBLE NOT NULL,
    `space_between_labels_horizontal_in_mm` DOUBLE NOT NULL,
    `space_between_labels_vertical_in_mm` DOUBLE NOT NULL,
    `is_label_border_visible` TINYINT(1) NOT NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
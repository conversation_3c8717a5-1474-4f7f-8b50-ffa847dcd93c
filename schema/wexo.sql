CREATE TABLE `wexo_shipping_method_type` (
    `id` BINARY(16) NOT NULL,
    `shipping_method_id` BINARY(16) NULL,
    `type_key` VARCHAR(255) NOT NULL,
    `penalties` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.wexo_shipping_method_type.penalties` CHECK (JSON_VALID(`penalties`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wexo_parcel_shop` (
    `id` BINARY(16) NOT NULL,
    `order_id` BINARY(16) NOT NULL,
    `order_version_id` BINARY(16) NOT NULL,
    `provider_id` VARCHAR(255) NOT NULL,
    `type_key` VARCHAR(255) NOT NULL,
    `latitude` DOUBLE NOT NULL,
    `longitude` DOUBLE NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `street` VARCHAR(255) NOT NULL,
    `zip_code` VARCHAR(255) NOT NULL,
    `country_code` VARCHAR(255) NOT NULL,
    `city` VARCHAR(255) NOT NULL,
    `note` VARCHAR(255) NULL,
    `telephone` VARCHAR(255) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wexo_shipping_method_deadline` (
    `id` BINARY(16) NOT NULL,
    `shipping_method_id` BINARY(16) NULL,
    `deadlines` JSON NOT NULL,
    `excluded_dates` JSON NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `json.wexo_shipping_method_deadline.deadlines` CHECK (JSON_VALID(`deadlines`)),
    CONSTRAINT `json.wexo_shipping_method_deadline.excluded_dates` CHECK (JSON_VALID(`excluded_dates`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wexo_shipping_method_config` (
    `id` BINARY(16) NOT NULL,
    `shipping_method_id` BINARY(16) NULL,
    `shipping_comment_required` TINYINT(1) NOT NULL DEFAULT '0',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wexo_shipping_comment` (
    `id` BINARY(16) NOT NULL,
    `order_id` BINARY(16) NOT NULL,
    `order_version_id` BINARY(16) NOT NULL,
    `comment` LONGTEXT NOT NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
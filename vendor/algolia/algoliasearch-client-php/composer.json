{"name": "algolia/algoliasearch-client-php", "description": "Algolia Search API Client for PHP", "keywords": ["algolia", "search", "api", "client", "php"], "type": "library", "license": "MIT", "authors": [{"name": "Algolia Team", "email": "<EMAIL>"}], "require": {"php": "^7.3 || ^8.0", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "fzaninotto/faker": "^1.8", "phpunit/phpunit": "^8.0 || ^9.0", "symfony/yaml": "^2.0 || ^4.0"}, "autoload": {"psr-4": {"Algolia\\AlgoliaSearch\\": "src/"}, "files": ["src/Http/Psr7/functions.php", "src/functions.php"]}, "autoload-dev": {"psr-4": {"Algolia\\AlgoliaSearch\\Tests\\": "tests"}}, "suggest": {"guzzlehttp/guzzle": "If you prefer to use Guzzle HTTP client instead of the Http Client implementation provided by the package"}, "bin": ["bin/algolia-doctor"], "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist"}, "extra": {"branch-alias": {"dev-2.0": "2.0.x-dev"}}, "scripts": {"lint": "php-cs-fixer fix", "test:lint": "php-cs-fixer fix -v --dry-run", "test:unit": "phpunit --colors=always", "test": ["@test:lint", "@test:unit"]}}
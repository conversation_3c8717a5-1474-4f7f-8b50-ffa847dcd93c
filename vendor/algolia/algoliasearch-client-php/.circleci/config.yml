# PHP CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-php/ for more details
#
version: 2.1


executors:
  php-docker: # declares a reusable executor
    parameters:
      version:
        description: "PHP version tag"
        type: string
    docker:
        - image: cimg/php:<<parameters.version>>

jobs:
  test:
    parameters:
      version:
        description: "PHP version tag"
        type: string
      http_client:
        description: "PHP Client"
        type: string

    executor:
      name: php-docker
      version: <<parameters.version>>

    steps:
      - checkout

      - run: sudo apt-get update
      - run: sudo apt-get install -y php-zip


      # Download and cache dependencies
      - restore_cache:
          keys:
            - composer-deps-<<parameters.version>>-{{ checksum "composer.json" }}
            # fallback to using the latest cache if no exact match is found
            #- composer-deps-

      - run: COMPOSER_MEMORY_LIMIT=-1 composer install -n --prefer-dist

      - save_cache:
          key: composer-deps-<<parameters.version>>-{{ checksum "composer.json" }}
          paths:
            - ./vendor

      - run:
          name: Install PHP Client
          command: |
            if [ "<<parameters.http_client>>" == "legacy" ]
            then
               echo "Nothing to install, using default Algolia\AlgoliaSearch\Http\Php53HttpClient"
            else
              COMPOSER_MEMORY_LIMIT=-1 composer require <<parameters.http_client>> -W
            fi

      - run:
          name: Get API Key Dealer client
          command: |
            if [ "${CIRCLE_PR_REPONAME}" ]
            then
              curl -s https://algoliasearch-client-keygen.herokuapp.com | bash >> "$BASH_ENV"
            fi

      - run:
          name: Check code styles
          command: PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix -v --dry-run

      # Run tests with phpunit 
      #
      # If the PR is open by an Algolia, we run all the tests
      # with the keys in the env variables
      # If the PR was open from a fork (community PR)
      # we get API keys from the API key dealer https://alg.li/api-key-dealer
      # we only run the tests on dictionaries on one case to avoid concurrency problems
      - run:
          name: Run tests
          command: |
            export CI_BUILD_NUM=$CIRCLE_BUILD_NUM
            if [ -z ${CIRCLE_PR_REPONAME+x} ]
            then
              if [ "<<parameters.version>>" == "latest" ] && [ "<<parameters.http_client>>" == "guzzlehttp/guzzle:"^7.0"" ]
              then
                 php vendor/bin/phpunit
              else
                 php vendor/bin/phpunit --exclude-group=dictionaries
              fi
            else
              export CI_PROJ_USERNAME=$CIRCLE_PR_USERNAME
              export CI_PROJ_REPONAME=$CIRCLE_PR_REPONAME

              if [ "<<parameters.version>>" == "latest" ] && [ "<<parameters.http_client>>" == "guzzlehttp/guzzle:"^7.0"" ]
              then
                 php vendor/bin/phpunit
              else
                 php vendor/bin/phpunit --exclude-group=dictionaries
              fi
            fi

      - run:
          name: Autoloading without Composer
          command: php tests/tests-no-composer.php


workflows:
  workflow:
    jobs:
      - test:
          name: 'Guzzle 7 - PHP 8.1'
          version: "8.1"
          http_client: guzzlehttp/guzzle:"^7.0"
      - test:
          name: 'Guzzle 7 - PHP 8.0'
          version: "8.0"
          http_client: guzzlehttp/guzzle:"^7.0"
      - test:
          name: 'Guzzle 7 - PHP 7.4'
          version: "7.4"
          http_client: guzzlehttp/guzzle:"^7.0"
      - test:
          name: 'Guzzle 6 - PHP 8.1'
          version: "8.1"
          http_client: guzzlehttp/guzzle:"^6.0"
      - test:
          name: 'Guzzle 6 - PHP 8.0'
          version: "8.0"
          http_client: guzzlehttp/guzzle:"^6.0"
      - test:
          name: 'Guzzle 6 - PHP 7.4'
          version: "7.4"
          http_client: guzzlehttp/guzzle:"^6.0"
      - test:
          name: 'Guzzle 6 - PHP 7.3'
          version: "7.3"
          http_client: guzzlehttp/guzzle:"^6.0"
      - test:
          name: 'Legacy client - PHP 8.1'
          version: "8.1"
          http_client: legacy
      - test:
          name: 'Legacy client - PHP 8.0'
          version: "8.0"
          http_client: legacy
      - test:
          name: 'Legacy client - PHP 7.4'
          version: "7.4"
          http_client: legacy
      - test:
          name: 'Legacy client - PHP 7.3'
          version: "7.3"
          http_client: legacy

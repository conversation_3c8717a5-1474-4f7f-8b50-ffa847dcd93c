{"name": "shopware/production", "type": "project", "license": "MIT", "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true, "php-http/discovery": true}}, "prefer-stable": true, "scripts": {"auto-scripts": {"assets:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "autoload": {"psr-4": {"Shopware\\Production\\": "src/"}}, "repositories": [{"type": "path", "url": "custom/plugins/*/packages/*", "options": {"symlink": true}}, {"type": "path", "url": "custom/static-plugins/*", "options": {"symlink": true}}, {"type": "path", "url": "custom/plugins/*", "options": {"symlink": true}}], "require": {"php": "^7.4.3 || ^8.0", "composer-runtime-api": "^2.0", "bs-algolia/bs-algolia": "1.0.0", "bs/oppo-suits": "1.0.0", "shopware/administration": "*******", "shopware/core": "*******", "shopware/elasticsearch": "*******", "shopware/storefront": "*******", "symfony/flex": "^2", "symfony/runtime": "^5.0|^6.0"}, "require-dev": {"shopware/dev-tools": "*"}, "extra": {"symfony": {"allow-contrib": true, "endpoint": ["https://raw.githubusercontent.com/shopware/recipes/flex/main/index.json", "flex://defaults"]}}}